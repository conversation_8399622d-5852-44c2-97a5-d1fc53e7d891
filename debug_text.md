[InsertSelectionBranchAfterStep] 选中元素: 初始步
[选择分支] 首次插入，现有分支: 0个
[选择分支] 新分支位置: 221,319.5
[选择分支] 创建: Initial, 位置: 221,319.5
[分支集合] 添加分支，位置: 221,319.5
[SFCBranchViewModel] 初始化适配器: BranchType=Selection, ElementType=SelectionBranch
[SFCBranchViewModel] ✅ 选择分支适配器配置完成，数量: 4
[SFCBranchViewModel] 🔧 索引1适配器类型: Input (修复为Input)
[SFCBranchViewModel] 🔧 索引2适配器类型: Output (修复为Output)
[分支集合] ViewModel位置: 221,319.5
[SFCCanvas] 为新创建的分支添加位置变化监听: f80de72b-1c8e-4940-b678-e590f43fd84a
[AddConnection] 开始执行: 53e9a870-475c-4fa2-ac12-4d1109b46e2e -> f80de72b-1c8e-4940-b678-e590f43fd84a
[AddConnection] 对象查找结果: sourceObject=SFCStepModel, targetObject=SFCBranchModel
[AddConnection] 位置获取: 源ViewModel位置=200,200, 源Model位置=200,200
[AddConnection] 位置获取: 目标ViewModel位置=221,319.5, 目标Model位置=221,319.5
[GetPreciseConnectPointPosition] ⚠️ 回退到计算方法
[GetPreciseConnectPointPosition] ⚠️ 回退到计算方法
[CalculateElementConnectPoint] SFCBranchModel: 类型=Selection, 位置=221,319.5, 输出点=False
[对比调试] 如果是步骤输入点，位置应该是: 271,325.5
[CalculateElementConnectPoint] ✅ 选择分支左上连接点(索引0): 242,326.5
[AddConnection] 创建连接: 53e9a870-475c-4fa2-ac12-4d1109b46e2e -> f80de72b-1c8e-4940-b678-e590f43fd84a
[AddConnection] 源位置: 200,200, 目标位置: 221,319.5
[AddConnection] 源对象类型: SFCStepModel, 目标对象类型: SFCBranchModel
[AddConnection] 源连接点: 250,322, 目标连接点: 242,326.5
[AddConnection] 连接创建完成，路径点数量: 2
[AddConnectionViewModel] 连接 61c4d98f-183f-4473-974c-64a8b4847945 路径点数量: 2
[AddConnectionViewModel] ViewModel路径点数量: 2
[AddConnectionViewModel] 起点: 250,322, 终点: 242,326.5
[AddConnection] 🔄 开始更新连接点状态: 53e9a870-475c-4fa2-ac12-4d1109b46e2e -> f80de72b-1c8e-4940-b678-e590f43fd84a
[UpdateElementConnectPointState] 🔍 开始更新连接点状态:
  元素类型: SFCStepViewModel
  连接点索引: 0
  是否输入: False
  连接ID: 61c4d98f-183f-4473-974c-64a8b4847945
[UpdateElementConnectPointState] ✅ 连接点状态更新成功: SFCStepViewModel, Direction=Output, Index=0, AdapterIndex=0, ConnectionId: 61c4d98f-183f-4473-974c-64a8b4847945
[UpdateElementConnectPointState] 🔍 开始更新连接点状态:
  元素类型: SFCBranchViewModel
  连接点索引: 0
  是否输入: True
  连接ID: 61c4d98f-183f-4473-974c-64a8b4847945
[UpdateElementConnectPointState] 🎯 找到分支元素适配器: f80de72b-1c8e-4940-b678-e590f43fd84a, 分支类型: Selection, 适配器数量: 4
[UpdateElementConnectPointState] ✅ 连接点状态更新成功: SFCBranchViewModel, Direction=Input, Index=0, AdapterIndex=0, ConnectionId: 61c4d98f-183f-4473-974c-64a8b4847945
[UpdateConnectPointStates] 连接点状态更新完成: 53e9a870-475c-4fa2-ac12-4d1109b46e2e[0] -> f80de72b-1c8e-4940-b678-e590f43fd84a[0]
[选择分支] 跳过垂直对齐，保持计算位置: 221,319.5
[选择分支] 左分支使用竖直连接线，无需创建转换条件
[选择分支] 右分支转换条件已内嵌在选择分支视图中，无需单独创建
System.Windows.Data Error: 40 : BindingExpression path error: 'Description' property not found on 'object' ''SFCBranchViewModel' (HashCode=43557827)'. BindingExpression:Path=SelectedElement.Description; DataItem='EnhancedSFCViewModel' (HashCode=34232944); target element is 'TextBlock' (Name=''); target property is 'Text' (type 'String')
[分支包络] 🎯 开始分支包络布局更新: 选择分支插入
[分支包络] 📋 正确方法：调整元素位置，而不是修改分支视图
  插入元素类型: SFCBranchModel
[包络分析] 🔍 开始分析包络上下文
[包络分析] 📦 检测到分支插入: f80de72b-1c8e-4940-b678-e590f43fd84a
[最近步骤] 🎯 找到最近步骤 53e9a870-475c-4fa2-ac12-4d1109b46e2e，距离: 121.3px
[父步骤查找] 🎯 基于位置找到最近步骤: 53e9a870-475c-4fa2-ac12-4d1109b46e2e
[包络分析] 🎯 找到父步骤: 53e9a870-475c-4fa2-ac12-4d1109b46e2e
[分支查找] ❌ 未找到包含步骤 53e9a870-475c-4fa2-ac12-4d1109b46e2e 的分支
[包络分析] ❌ 未找到包含父步骤的分支
[分支包络] ℹ️ 非包络场景，跳过处理
[InitializeConnectPoints] 开始初始化连接点，ViewModel.Id: f80de72b-1c8e-4940-b678-e590f43fd84a
[InitializeConnectPoints] XAML连接点查找结果: LeftTop=True, LeftBottom=True, RightTop=True, RightBottom=True
[SFCSelectionBranchView] 开始设置连接点ElementId: f80de72b-1c8e-4940-b678-e590f43fd84a
[SFCSelectionBranchView] ✅ LeftTop连接点ElementId设置: f80de72b-1c8e-4940-b678-e590f43fd84a
[SFCSelectionBranchView] ✅ LeftBottom连接点ElementId设置: f80de72b-1c8e-4940-b678-e590f43fd84a
[SFCSelectionBranchView] ✅ RightTop连接点ElementId设置: f80de72b-1c8e-4940-b678-e590f43fd84a
[SFCSelectionBranchView] ✅ RightBottom连接点ElementId设置: f80de72b-1c8e-4940-b678-e590f43fd84a
[InitializeConnectPoints] LeftTop连接点ElementId: 'f80de72b-1c8e-4940-b678-e590f43fd84a', 期望: 'f80de72b-1c8e-4940-b678-e590f43fd84a'
[InitializeConnectPoints] ✅ 已修复LeftTop连接点ElementId: 'f80de72b-1c8e-4940-b678-e590f43fd84a'
[SFCSelectionBranchView] 适配器检查: Count=4, 期望>=4
[SFCSelectionBranchView] ✅ LeftTop适配器绑定: Direction=Input, Index=0
[SFCSelectionBranchView] ✅ LeftBottom适配器绑定: Direction=Input, Index=1
[SFCSelectionBranchView] ✅ RightTop适配器绑定: Direction=Output, Index=2
[SFCSelectionBranchView] ✅ RightBottom适配器绑定: Direction=Output, Index=3
[SFCSelectionBranchView] 开始同步现有连接状态: f80de72b-1c8e-4940-b678-e590f43fd84a
[SFCSelectionBranchView] 找到 1 个相关连接
[SFCSelectionBranchView] ✅ 同步输入连接: 61c4d98f-183f-4473-974c-64a8b4847945, 索引: 0
System.Windows.Data Error: 40 : BindingExpression path error: 'IsInitialStep' property not found on 'object' ''SFCBranchViewModel' (HashCode=43557827)'. BindingExpression:Path=IsInitialStep; DataItem='SFCBranchViewModel' (HashCode=43557827); target element is 'SFCConnectPoint' (Name='LeftTopConnectPoint'); target property is 'NoTarget' (type 'Object')
System.Windows.Data Error: 40 : BindingExpression path error: 'IsInitialStep' property not found on 'object' ''SFCBranchViewModel' (HashCode=43557827)'. BindingExpression:Path=IsInitialStep; DataItem='SFCBranchViewModel' (HashCode=43557827); target element is 'SFCConnectPoint' (Name='LeftBottomConnectPoint'); target property is 'NoTarget' (type 'Object')
System.Windows.Data Error: 40 : BindingExpression path error: 'IsInitialStep' property not found on 'object' ''SFCBranchViewModel' (HashCode=43557827)'. BindingExpression:Path=IsInitialStep; DataItem='SFCBranchViewModel' (HashCode=43557827); target element is 'SFCConnectPoint' (Name='TopConnectPoint1'); target property is 'NoTarget' (type 'Object')
System.Windows.Data Error: 40 : BindingExpression path error: 'IsInitialStep' property not found on 'object' ''SFCBranchViewModel' (HashCode=43557827)'. BindingExpression:Path=IsInitialStep; DataItem='SFCBranchViewModel' (HashCode=43557827); target element is 'SFCConnectPoint' (Name='TopConnectPoint2'); target property is 'NoTarget' (type 'Object')
System.Windows.Data Error: 40 : BindingExpression path error: 'IsInitialStep' property not found on 'object' ''SFCBranchViewModel' (HashCode=43557827)'. BindingExpression:Path=IsInitialStep; DataItem='SFCBranchViewModel' (HashCode=43557827); target element is 'SFCConnectPoint' (Name='LeftTopConnectPoint'); target property is 'NoTarget' (type 'Object')
System.Windows.Data Error: 40 : BindingExpression path error: 'IsInitialStep' property not found on 'object' ''SFCBranchViewModel' (HashCode=43557827)'. BindingExpression:Path=IsInitialStep; DataItem='SFCBranchViewModel' (HashCode=43557827); target element is 'SFCConnectPoint' (Name='LeftParallelPoint'); target property is 'NoTarget' (type 'Object')
System.Windows.Data Error: 40 : BindingExpression path error: 'IsInitialStep' property not found on 'object' ''SFCBranchViewModel' (HashCode=43557827)'. BindingExpression:Path=IsInitialStep; DataItem='SFCBranchViewModel' (HashCode=43557827); target element is 'SFCConnectPoint' (Name='RightParallelPoint'); target property is 'NoTarget' (type 'Object')
[InitializeConnectPoints] 连接点已初始化，跳过重复初始化: f80de72b-1c8e-4940-b678-e590f43fd84a
System.Windows.Data Error: 4 : Cannot find source for binding with reference 'RelativeSource FindAncestor, AncestorType='System.Windows.Controls.UserControl', AncestorLevel='1''. BindingExpression:Path=DataContext.InsertSelectionBranchFromAnyPartCommand; DataItem=null; target element is 'MenuItem' (Name=''); target property is 'Command' (type 'ICommand')
System.Windows.Data Error: 4 : Cannot find source for binding with reference 'RelativeSource FindAncestor, AncestorType='System.Windows.Controls.UserControl', AncestorLevel='1''. BindingExpression:Path=DataContext.InsertSiblingBranchCommand; DataItem=null; target element is 'MenuItem' (Name=''); target property is 'Command' (type 'ICommand')
System.Windows.Data Error: 4 : Cannot find source for binding with reference 'RelativeSource FindAncestor, AncestorType='System.Windows.Controls.UserControl', AncestorLevel='1''. BindingExpression:Path=DataContext.InsertParallelBranchFromAnyPartCommand; DataItem=null; target element is 'MenuItem' (Name=''); target property is 'Command' (type 'ICommand')
[连接点重叠检测] 点1: (250.0, 322.0), 点2: (242.0, 326.5), 距离: 9.2px, 重叠: True
[CreateConnectionPath] 连接 61c4d98f-183f-4473-974c-64a8b4847945 连接点重叠，隐藏连接线
[CreateConnectionPath] 起点: (250.0, 322.0), 终点: (242.0, 326.5)
连接 61c4d98f-183f-4473-974c-64a8b4847945 连接点重叠，使用连接点重叠方式
[AddConnection] 延迟更新连接线 61c4d98f-183f-4473-974c-64a8b4847945 的路径点
[CalculateElementConnectPoint] ✅ 选择分支ViewModel左上连接点(索引0): 242,326.5
[AddConnection] 延迟更新 - 源对象类型: SFCStepViewModel, 目标对象类型: SFCBranchViewModel
[AddConnection] 延迟更新 - 源位置: 200,200, 目标位置: 221,319.5
[AddConnection] 延迟更新后的路径点: 250,322 -> 242,326.5
