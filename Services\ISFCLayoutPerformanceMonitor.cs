using System;

namespace PC_Control2.Demo.Services
{
    /// <summary>
    /// SFC布局性能监控接口
    /// </summary>
    public interface ISFCLayoutPerformanceMonitor
    {
        /// <summary>
        /// 开始布局计算监控
        /// </summary>
        string StartLayoutCalculationMonitoring(string branchId, string operationType);

        /// <summary>
        /// 结束布局计算监控
        /// </summary>
        void EndLayoutCalculationMonitoring(string monitoringId, LayoutCalculationResult result);
    }

    /// <summary>
    /// 布局计算结果
    /// </summary>
    public class LayoutCalculationResult
    {
        public bool IsSuccessful { get; set; }
        public int ProcessedElementCount { get; set; }
        public int CacheHitCount { get; set; }
        public int CacheMissCount { get; set; }
        public string ErrorMessage { get; set; } = string.Empty;
    }

    /// <summary>
    /// 简单的性能监控实现
    /// </summary>
    public class SFCLayoutPerformanceMonitor : ISFCLayoutPerformanceMonitor
    {
        public string StartLayoutCalculationMonitoring(string branchId, string operationType)
        {
            var monitoringId = Guid.NewGuid().ToString("N")[..8];
            System.Diagnostics.Debug.WriteLine($"[性能监控] 🚀 开始监控 {operationType} - {branchId} (ID: {monitoringId})");
            return monitoringId;
        }

        public void EndLayoutCalculationMonitoring(string monitoringId, LayoutCalculationResult result)
        {
            var status = result.IsSuccessful ? "✅ 成功" : "❌ 失败";
            System.Diagnostics.Debug.WriteLine($"[性能监控] 🏁 结束监控 (ID: {monitoringId}) - {status}");
            System.Diagnostics.Debug.WriteLine($"  处理元素: {result.ProcessedElementCount}, 缓存命中: {result.CacheHitCount}");
        }
    }
}