using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;

namespace PC_Control2.Demo.Models
{
    /// <summary>
    /// SFC嵌套检测器实现
    /// 负责检测分支内的嵌套元素和层次关系
    /// </summary>
    public class SFCNestingDetectorImpl : ISFCNestingDetector
    {
        #region 私有字段

        private readonly double _spatialConflictThreshold = 10.0;
        private readonly double _minimumBranchSpacing = 20.0;
        private readonly int _maxNestingDepth = 10;

        #endregion

        #region ISFCNestingDetector 接口实现

        /// <summary>
        /// 检测嵌套元素
        /// </summary>
        public List<NestedElementInfo> DetectNestedElements(SFCBranchModel branch, SFCModel context)
        {
            var nestedElements = new List<NestedElementInfo>();

            try
            {
                // 获取分支的空间信息
                var parentSpace = GetBranchSpatialInfo(branch);

                // 检测嵌套的分支
                foreach (var otherBranch in context.Branches.Where(b => b.Id != branch.Id))
                {
                    var branchSpace = GetBranchSpatialInfo(otherBranch);
                    if (IsContainedWithin(branchSpace.Position, branchSpace.Size, parentSpace.Position, parentSpace.Size))
                    {
                        var nestedInfo = new NestedElementInfo
                        {
                            ElementId = otherBranch.Id,
                            ElementType = SFCElementType.Branch,
                            NestingLevel = CalculateNestingDepth(otherBranch, context),
                            SpatialInfo = branchSpace
                        };
                        nestedElements.Add(nestedInfo);
                    }
                }

                // 检测嵌套的步骤
                foreach (var step in context.Steps)
                {
                    var stepSpace = GetStepSpatialInfo(step);
                    if (IsContainedWithin(stepSpace.Position, stepSpace.Size, parentSpace.Position, parentSpace.Size))
                    {
                        var nestedInfo = new NestedElementInfo
                        {
                            ElementId = step.Id,
                            ElementType = SFCElementType.Step,
                            NestingLevel = 1,
                            SpatialInfo = stepSpace
                        };
                        nestedElements.Add(nestedInfo);
                    }
                }

                // 检测嵌套的转换
                foreach (var transition in context.Transitions)
                {
                    var transitionSpace = GetTransitionSpatialInfo(transition);
                    if (IsContainedWithin(transitionSpace.Position, transitionSpace.Size, parentSpace.Position, parentSpace.Size))
                    {
                        var nestedInfo = new NestedElementInfo
                        {
                            ElementId = transition.Id,
                            ElementType = SFCElementType.Transition,
                            NestingLevel = 1,
                            SpatialInfo = transitionSpace
                        };
                        nestedElements.Add(nestedInfo);
                    }
                }
            }
            catch (Exception ex)
            {
                // 记录错误但不抛出异常
                System.Diagnostics.Debug.WriteLine($"检测嵌套元素时发生错误: {ex.Message}");
            }

            return nestedElements;
        }

        /// <summary>
        /// 计算嵌套深度
        /// </summary>
        public int CalculateNestingDepth(SFCBranchModel branch, SFCModel context)
        {
            try
            {
                int depth = 0;
                var currentBranch = branch;

                while (currentBranch != null && depth < _maxNestingDepth)
                {
                    if (string.IsNullOrEmpty(currentBranch.ParentBranchId))
                        break;

                    var parentBranch = context.Branches.FirstOrDefault(b => b.Id == currentBranch.ParentBranchId);
                    if (parentBranch == null)
                        break;

                    depth++;
                    currentBranch = parentBranch;
                }

                return depth;
            }
            catch
            {
                return 0;
            }
        }

        /// <summary>
        /// 检查是否有嵌套冲突
        /// </summary>
        public bool HasNestedConflicts(SFCBranchModel branch, SFCModel context)
        {
            try
            {
                var nestedElements = DetectNestedElements(branch, context);
                return nestedElements.Any(element => HasSpatialConflict(element, branch));
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 获取祖先分支
        /// </summary>
        public List<SFCBranchModel> GetAncestorBranches(SFCBranchModel branch, SFCModel context)
        {
            var ancestors = new List<SFCBranchModel>();

            try
            {
                var currentBranch = branch;
                int depth = 0;

                while (currentBranch != null && depth < _maxNestingDepth)
                {
                    if (string.IsNullOrEmpty(currentBranch.ParentBranchId))
                        break;

                    var parentBranch = context.Branches.FirstOrDefault(b => b.Id == currentBranch.ParentBranchId);
                    if (parentBranch == null)
                        break;

                    ancestors.Add(parentBranch);
                    currentBranch = parentBranch;
                    depth++;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"获取祖先分支时发生错误: {ex.Message}");
            }

            return ancestors;
        }

        /// <summary>
        /// 获取后代分支
        /// </summary>
        public List<SFCBranchModel> GetDescendantBranches(SFCBranchModel branch, SFCModel context)
        {
            var descendants = new List<SFCBranchModel>();

            try
            {
                GetDescendantBranchesRecursive(branch, context, descendants, 0);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"获取后代分支时发生错误: {ex.Message}");
            }

            return descendants;
        }

        #endregion

        #region 私有辅助方法

        /// <summary>
        /// 获取分支空间信息
        /// </summary>
        private BranchSpatialInfo GetBranchSpatialInfo(SFCBranchModel branch)
        {
            return new BranchSpatialInfo
            {
                ElementId = branch.Id,
                Position = branch.Position,
                Size = branch.CalculatedSize,
                OccupiedSpace = branch.OccupiedSpace,
                MinimumSpace = branch.MinimumSpace
            };
        }

        /// <summary>
        /// 获取步骤空间信息
        /// </summary>
        private BranchSpatialInfo GetStepSpatialInfo(SFCStepModel step)
        {
            return new BranchSpatialInfo
            {
                ElementId = step.Id,
                Position = step.Position,
                Size = new Size(80, 40), // 步骤的默认大小
                OccupiedSpace = new Size(80, 40),
                MinimumSpace = new Size(80, 40)
            };
        }

        /// <summary>
        /// 获取转换空间信息
        /// </summary>
        private BranchSpatialInfo GetTransitionSpatialInfo(SFCTransitionModel transition)
        {
            return new BranchSpatialInfo
            {
                ElementId = transition.Id,
                Position = transition.Position,
                Size = new Size(60, 20), // 转换的默认大小
                OccupiedSpace = new Size(60, 20),
                MinimumSpace = new Size(60, 20)
            };
        }

        /// <summary>
        /// 检查是否包含在内
        /// </summary>
        private bool IsContainedWithin(Point childPos, Size childSize, Point parentPos, Size parentSize)
        {
            var childRect = new Rect(childPos, childSize);
            var parentRect = new Rect(parentPos, parentSize);
            return parentRect.Contains(childRect);
        }

        /// <summary>
        /// 检查是否有空间冲突
        /// </summary>
        private bool HasSpatialConflict(NestedElementInfo element, SFCBranchModel parentBranch)
        {
            try
            {
                var elementRect = new Rect(element.SpatialInfo.Position, element.SpatialInfo.Size);
                var parentRect = new Rect(parentBranch.Position, parentBranch.CalculatedSize);

                // 检查是否超出边界
                if (!parentRect.Contains(elementRect))
                    return true;

                // 检查是否距离边界太近
                var margin = _spatialConflictThreshold;
                var innerRect = new Rect(
                    parentRect.X + margin,
                    parentRect.Y + margin,
                    Math.Max(0, parentRect.Width - 2 * margin),
                    Math.Max(0, parentRect.Height - 2 * margin)
                );

                return !innerRect.Contains(elementRect);
            }
            catch
            {
                return true; // 发生错误时认为有冲突
            }
        }

        /// <summary>
        /// 递归获取后代分支
        /// </summary>
        private void GetDescendantBranchesRecursive(SFCBranchModel branch, SFCModel context, List<SFCBranchModel> descendants, int depth)
        {
            if (depth >= _maxNestingDepth)
                return;

            var childBranches = context.Branches.Where(b => b.ParentBranchId == branch.Id);
            foreach (var childBranch in childBranches)
            {
                descendants.Add(childBranch);
                GetDescendantBranchesRecursive(childBranch, context, descendants, depth + 1);
            }
        }

        #endregion
    }
}