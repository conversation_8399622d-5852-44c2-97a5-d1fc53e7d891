using System.Collections.Generic;
using System.Windows;
using System.Windows.Input;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using PC_Control2.Demo.Models;

namespace PC_Control2.Demo.ViewModels
{
    /// <summary>
    /// SFC分支的ViewModel - 完整实现
    /// </summary>
    public class SFCBranchViewModel : ObservableObject
    {
        private string _id = string.Empty;
        private string _name = string.Empty;
        private SFCBranchType _branchType = SFCBranchType.Selection;
        private Point _position = new Point(0, 0);
        private Size _size = new Size(120, 12);
        private Size _calculatedSize = new Size(120, 12); // 🆕 动态计算的尺寸
        private double _horizontalLineExtension = 44; // 🆕 水平线扩展长度
        private bool _isSelected = false;
        private bool _isConvergence = false;
        private string _selectedPart = "None"; // 选择分支的选中部分：Left, Right, None
        private BranchViewType _viewType = BranchViewType.Initial; // 分支显示类型
        private string? _nextBranchId = null; // 下一个兄弟分支的ID
        private int _internalTransitionNumber = 0; // 内部转换条件编号
        private string _internalTransitionCondition = "TRUE"; // 内部转换条件表达式

        public string Id
        {
            get => _id;
            set => SetProperty(ref _id, value);
        }

        public string Name
        {
            get => _name;
            set => SetProperty(ref _name, value);
        }

        public SFCBranchType BranchType
        {
            get => _branchType;
            set
            {
                if (SetProperty(ref _branchType, value))
                {
                    // 🔧 关键修复：当分支类型变化时，重新初始化适配器
                    // 这解决了构造函数时序问题：构造时BranchType还是默认值，属性设置时才是正确值
                    InitializeConnectPointAdapters();
                    System.Diagnostics.Debug.WriteLine($"[SFCBranchViewModel] 🔄 分支类型变更为{value}，重新初始化适配器，适配器数量: {ConnectPointAdapters.Count}");
                }
            }
        }

        public Point Position
        {
            get => _position;
            set => SetProperty(ref _position, value);
        }

        public Size Size
        {
            get => _size;
            set => SetProperty(ref _size, value);
        }
        /// <summary>
        /// 🆕 动态计算的分支尺寸，用于UI绑定
        /// </summary>
        public Size CalculatedSize
        {
            get => _calculatedSize;
            set => SetProperty(ref _calculatedSize, value);
        }

        /// <summary>
        /// 🆕 水平线扩展长度，用于动态调整分支的水平线长度
        /// </summary>
        public double HorizontalLineExtension
        {
            get => _horizontalLineExtension;
            set 
            {
                if (SetProperty(ref _horizontalLineExtension, value))
                {
                    OnPropertyChanged(nameof(CenterHorizontalLineX2));
                }
            }
        }
        /// <summary>
        /// 中间横线的X2坐标，基于HorizontalLineExtension动态计算
        /// </summary>
        public double CenterHorizontalLineX2
        {
            get => 44 + HorizontalLineExtension;
        }

        public bool IsSelected
        {
            get => _isSelected;
            set => SetProperty(ref _isSelected, value);
        }

        public bool IsConvergence
        {
            get => _isConvergence;
            set => SetProperty(ref _isConvergence, value);
        }

        /// <summary>
        /// 选择分支的选中部分：Left表示左侧竖线部分，Right表示右侧转换条件部分，None表示未选中
        /// </summary>
        public string SelectedPart
        {
            get => _selectedPart;
            set => SetProperty(ref _selectedPart, value);
        }

        /// <summary>
        /// 分支显示类型：Initial表示首次分支样式，Subsequent表示后续分支样式
        /// </summary>
        public BranchViewType ViewType
        {
            get => _viewType;
            set => SetProperty(ref _viewType, value);
        }

        /// <summary>
        /// 下一个兄弟分支的ID，用于构建分支链
        /// </summary>
        public string? NextBranchId
        {
            get => _nextBranchId;
            set => SetProperty(ref _nextBranchId, value);
        }

        public int InternalTransitionNumber
        {
            get => _internalTransitionNumber;
            set
            {
                if (SetProperty(ref _internalTransitionNumber, value))
                {
                    OnPropertyChanged(nameof(InternalTransitionDisplayText));
                    OnPropertyChanged(nameof(InternalTransitionHasLabel));
                }
            }
        }

        public string InternalTransitionCondition
        {
            get => _internalTransitionCondition;
            set => SetProperty(ref _internalTransitionCondition, value);
        }

        // 内部转换条件的显示文本（格式：T{编号}）
        public string InternalTransitionDisplayText => InternalTransitionNumber > 0 ? $"T{InternalTransitionNumber}" : string.Empty;

        // 内部转换条件是否有标签
        public bool InternalTransitionHasLabel => InternalTransitionNumber > 0;

        // 分支信息
        public List<string> InputStepIds { get; set; } = new();
        public List<string> OutputStepIds { get; set; } = new();

        // 命令
        public ICommand? SelectCommand { get; set; }
        public ICommand? DeleteCommand { get; set; }
        public ICommand? StartConnectionCommand { get; set; }
        public ICommand? EditPropertiesCommand { get; set; }
        public ICommand ToggleBranchTypeCommand { get; }
        public ICommand ToggleConvergenceCommand { get; }

        /// <summary>
        /// 连接点适配器集合
        /// </summary>
        public System.Collections.ObjectModel.ObservableCollection<SFCConnectPointAdapter> ConnectPointAdapters { get; } = new System.Collections.ObjectModel.ObservableCollection<SFCConnectPointAdapter>();

        public SFCBranchViewModel()
        {
            ToggleBranchTypeCommand = new RelayCommand(ToggleBranchType);
            ToggleConvergenceCommand = new RelayCommand(ToggleConvergence);
            
            // 初始化连接点适配器
            InitializeConnectPointAdapters();
        }
        
        /// <summary>
        /// 初始化连接点适配器
        /// </summary>
        private void InitializeConnectPointAdapters()
        {
            // 🔧 关键修复：清空现有适配器，避免重复添加
            ConnectPointAdapters.Clear();

            // 🔧 关键修复：根据分支类型确定正确的ElementType
            var elementType = BranchType == SFCBranchType.Parallel ? SFCElementType.ParallelBranch : SFCElementType.SelectionBranch;

            System.Diagnostics.Debug.WriteLine($"[SFCBranchViewModel] 初始化适配器: BranchType={BranchType}, ElementType={elementType}");

            // 🔧 选择分支适配器配置
            // UI连接点索引：LeftTop=0, LeftBottom=1, RightTop=2, RightBottom=3

            // 创建左侧输入连接点适配器 (UI Index=0)
            var leftInputAdapter = new SFCConnectPointAdapter(
                Id,
                elementType,
                ConnectPointDirection.Input,
                0,
                SFCDataFlowType.ControlFlow);

            // 🔧 关键修复：创建左侧连接点适配器 (UI Index=1)
            // 根据CreateBranchChainConnection逻辑，扩展选择分支的索引1用作输入
            var leftOutputAdapter = new SFCConnectPointAdapter(
                Id,
                elementType,
                ConnectPointDirection.Input,   // 🔧 关键修复：索引1用作输入（扩展分支连接的目标）
                1,
                SFCDataFlowType.ControlFlow);

            // 🔧 关键修复：创建右侧输出连接点适配器 (UI Index=2)
            // 索引2用于分支链连接，应该是Output类型
            var rightOutputAdapter2 = new SFCConnectPointAdapter(
                Id,
                elementType,
                ConnectPointDirection.Output,  // 🔧 关键修复：从Input改为Output
                2,
                SFCDataFlowType.ControlFlow);

            // 创建右侧输出连接点适配器 (UI Index=3)
            var rightOutputAdapter = new SFCConnectPointAdapter(
                Id,
                elementType,
                ConnectPointDirection.Output,
                3,
                SFCDataFlowType.ControlFlow);

            // 🔧 并行分支特殊处理：并行分支只有3个连接点，需要特殊的适配器配置
            if (BranchType == SFCBranchType.Parallel)
            {
                // 🔧 关键修复：根据CreateBranchChainConnection中的连接逻辑调整适配器配置
                // 连接逻辑：源分支索引0（输出）-> 目标分支索引1（输入）
                // 因此：索引0应该是输出，索引1应该是输入

                var parallelLeftTopAdapter = new SFCConnectPointAdapter(
                    Id,
                    SFCElementType.ParallelBranch,
                    ConnectPointDirection.Output,  // 🔧 关键修复：索引0用作输出（分支链连接的源）
                    0,
                    SFCDataFlowType.ControlFlow);

                var parallelLeftParallelAdapter = new SFCConnectPointAdapter(
                    Id,
                    SFCElementType.ParallelBranch,
                    ConnectPointDirection.Input,   // 🔧 关键修复：索引1用作输入（分支链连接的目标）
                    1,
                    SFCDataFlowType.ControlFlow);

                var parallelRightParallelAdapter = new SFCConnectPointAdapter(
                    Id,
                    SFCElementType.ParallelBranch,
                    ConnectPointDirection.Output,
                    2,
                    SFCDataFlowType.ControlFlow);

                ConnectPointAdapters.Add(parallelLeftTopAdapter);      // 索引0
                ConnectPointAdapters.Add(parallelLeftParallelAdapter); // 索引1
                ConnectPointAdapters.Add(parallelRightParallelAdapter);// 索引2

                System.Diagnostics.Debug.WriteLine($"[SFCBranchViewModel] ✅ 并行分支适配器配置完成，数量: {ConnectPointAdapters.Count}");
            }
            else
            {
                // 🔧 关键修复：选择分支适配器配置 - 根据CreateBranchChainConnection连接逻辑配置
                // 连接逻辑：源分支索引2（输出）-> 目标分支索引1（输入）或索引0（输入）
                ConnectPointAdapters.Add(leftInputAdapter);     // 索引0: LeftTop (Input) - 初始分支连接目标
                ConnectPointAdapters.Add(leftOutputAdapter);    // 索引1: LeftBottom (Input) - 扩展分支连接目标
                ConnectPointAdapters.Add(rightOutputAdapter2);  // 索引2: RightTop (Output) - 分支链连接源
                ConnectPointAdapters.Add(rightOutputAdapter);   // 索引3: RightBottom (Output) - 普通输出

                System.Diagnostics.Debug.WriteLine($"[SFCBranchViewModel] ✅ 选择分支适配器配置完成，数量: {ConnectPointAdapters.Count}");
                System.Diagnostics.Debug.WriteLine($"[SFCBranchViewModel] 🔧 索引1适配器类型: {ConnectPointAdapters[1].Direction} (修复为Input)");
                System.Diagnostics.Debug.WriteLine($"[SFCBranchViewModel] 🔧 索引2适配器类型: {ConnectPointAdapters[2].Direction} (修复为Output)");
            }
        }

        /// <summary>
        /// 切换分支类型（选择/并行）
        /// </summary>
        private void ToggleBranchType()
        {
            BranchType = BranchType == SFCBranchType.Selection
                ? SFCBranchType.Parallel
                : SFCBranchType.Selection;
        }

        /// <summary>
        /// 切换汇聚状态
        /// </summary>
        private void ToggleConvergence()
        {
            IsConvergence = !IsConvergence;
        }
    }
} 