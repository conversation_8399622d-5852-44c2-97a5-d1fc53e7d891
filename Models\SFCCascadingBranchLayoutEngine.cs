using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;

namespace PC_Control2.Demo.Models
{
    /// <summary>
    /// 级联分支布局引擎实现
    /// 实现自底向上的尺寸计算和自顶向下的位置更新
    /// </summary>
    public class SFCCascadingBranchLayoutEngine : ISFCBranchLayoutEngine
    {
        #region 私有字段

        private readonly ISFCNestingDetector _nestingDetector;
        private readonly ISFCBranchSizeCalculator _sizeCalculator;
        private readonly ILayoutCacheManager _cacheManager;
        private readonly ISFCBranchHierarchyManager _hierarchyManager;

        // 布局配置参数
        private readonly double _minimumBranchWidth = 120.0;
        private readonly double _minimumBranchHeight = 12.0;
        private readonly double _branchPadding = 10.0;
        private readonly double _nestedElementSpacing = 15.0;
        private readonly double _connectionPointSpacing = 8.0;

        // 性能监控
        private readonly Dictionary<string, TimeSpan> _performanceMetrics = new();

        #endregion

        #region 构造函数

        public SFCCascadingBranchLayoutEngine(
            ISFCNestingDetector nestingDetector,
            ISFCBranchSizeCalculator sizeCalculator,
            ILayoutCacheManager cacheManager,
            ISFCBranchHierarchyManager hierarchyManager)
        {
            _nestingDetector = nestingDetector ?? throw new ArgumentNullException(nameof(nestingDetector));
            _sizeCalculator = sizeCalculator ?? throw new ArgumentNullException(nameof(sizeCalculator));
            _cacheManager = cacheManager ?? throw new ArgumentNullException(nameof(cacheManager));
            _hierarchyManager = hierarchyManager ?? throw new ArgumentNullException(nameof(hierarchyManager));
        }

        #endregion

        #region ISFCBranchLayoutEngine 实现

        /// <summary>
        /// 异步计算分支布局
        /// </summary>
        public async Task<BranchLayoutResult> CalculateLayoutAsync(SFCBranchModel branch, LayoutChangeContext context)
        {
            var startTime = DateTime.Now;
            
            try
            {
                // 1. 检查缓存
                var cacheKey = GenerateLayoutCacheKey(branch, context);
                var cachedResult = _cacheManager.GetCachedLayout(branch.Id, cacheKey);
                if (cachedResult != null)
                {
                    RecordPerformanceMetric("CacheHit", DateTime.Now - startTime);
                    return cachedResult.Result;
                }

                // 2. 执行级联布局计算
                var layoutResult = await PerformCascadingLayoutCalculation(branch, context);

                // 3. 缓存结果
                _cacheManager.CacheLayout(branch.Id, cacheKey, layoutResult);

                RecordPerformanceMetric("LayoutCalculation", DateTime.Now - startTime);
                return layoutResult;
            }
            catch (Exception ex)
            {
                RecordPerformanceMetric("LayoutError", DateTime.Now - startTime);
                throw new InvalidOperationException($"布局计算失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 检查是否需要布局更新
        /// </summary>
        public bool IsLayoutUpdateNeeded(SFCBranchModel branch, LayoutChangeContext context)
        {
            if (branch == null) return false;

            // 检查分支自身是否需要更新
            if (branch.IsLayoutDirty) return true;

            // 检查子分支是否需要更新
            foreach (var childId in branch.ChildBranchIds)
            {
                var childBranch = context.SFCModel?.Branches?.FirstOrDefault(b => b.Id == childId);
                if (childBranch?.IsLayoutDirty == true) return true;
            }

            // 检查嵌套元素是否发生变化
            var nestedElements = _nestingDetector.DetectNestedElements(branch, context.SFCModel);
            if (nestedElements.Count != branch.ChildBranchIds.Count) return true;

            return false;
        }

        /// <summary>
        /// 使布局失效
        /// </summary>
        public void InvalidateLayout(SFCBranchModel branch)
        {
            if (branch == null) return;

            // 标记分支布局为脏
            branch.MarkLayoutDirty();

            // 递归标记所有父分支
            var parentBranch = GetParentBranch(branch);
            if (parentBranch != null)
            {
                InvalidateLayout(parentBranch);
            }

            // 清除相关缓存
            _cacheManager.InvalidateCache(branch.Id);
        }

        /// <summary>
        /// 批量计算多个分支的布局
        /// </summary>
        public async Task<Dictionary<string, BranchLayoutResult>> CalculateBatchLayoutAsync(
            IEnumerable<SFCBranchModel> branches, 
            LayoutChangeContext context)
        {
            var results = new Dictionary<string, BranchLayoutResult>();
            var startTime = DateTime.Now;

            try
            {
                // 按层次排序分支（先计算子分支，再计算父分支）
                var sortedBranches = SortBranchesByHierarchy(branches);

                // 并行计算独立分支的布局
                var tasks = new List<Task<KeyValuePair<string, BranchLayoutResult>>>();

                foreach (var branch in sortedBranches)
                {
                    tasks.Add(CalculateSingleBranchLayoutAsync(branch, context));
                }

                var completedTasks = await Task.WhenAll(tasks);

                foreach (var result in completedTasks)
                {
                    results[result.Key] = result.Value;
                }

                RecordPerformanceMetric("BatchLayoutCalculation", DateTime.Now - startTime);
                return results;
            }
            catch (Exception ex)
            {
                RecordPerformanceMetric("BatchLayoutError", DateTime.Now - startTime);
                throw new InvalidOperationException($"批量布局计算失败: {ex.Message}", ex);
            }
        }

        #endregion

        #region 核心布局算法

        /// <summary>
        /// 执行级联布局计算
        /// </summary>
        private async Task<BranchLayoutResult> PerformCascadingLayoutCalculation(SFCBranchModel branch, LayoutChangeContext context)
        {
            var startTime = DateTime.Now;
            
            // 检测嵌套元素
            var nestedElements = _nestingDetector.DetectNestedElements(branch, context.SFCModel ?? new SFCModel());
            
            // 1. 自底向上计算尺寸
            var calculatedSize = await CalculateBottomUpSize(branch, context);

            // 2. 自顶向下更新位置
            var updatedPositions = await CalculateTopDownPositions(branch, context, calculatedSize);

            // 3. 更新连接点位置
            var connectionPointPositions = CalculateConnectionPointPositions(branch, calculatedSize);

            // 4. 检测并解决冲突
            var conflictResolution = await ResolveLayoutConflicts(branch, context, calculatedSize);

            // 5. 构建布局结果
            var layoutResult = new BranchLayoutResult
            {
                BranchId = branch.Id,
                CalculatedSize = calculatedSize,
                ConnectPointPositions = connectionPointPositions,
                NestedElements = nestedElements,
                CalculationTime = DateTime.Now - startTime,
                IsValid = true,
                HorizontalLineExtension = CalculateHorizontalLineExtension(branch, nestedElements),
                Timestamp = DateTime.Now
            };

            // 6. 应用布局结果到分支模型
            ApplyLayoutResultToBranch(branch, layoutResult);

            return layoutResult;
        }

        /// <summary>
        /// 自底向上计算尺寸
        /// </summary>
        private async Task<Size> CalculateBottomUpSize(SFCBranchModel branch, LayoutChangeContext context)
        {
            var startTime = DateTime.Now;

            try
            {
                // 1. 获取嵌套元素
                var nestedElements = _nestingDetector.DetectNestedElements(branch, context.SFCModel);

                // 2. 计算子分支的尺寸（递归）
                var childSizes = new Dictionary<string, Size>();
                foreach (var childId in branch.ChildBranchIds)
                {
                    var childBranch = context.SFCModel?.Branches?.FirstOrDefault(b => b.Id == childId);
                    if (childBranch != null)
                    {
                        var childSize = await CalculateBottomUpSize(childBranch, context);
                        childSizes[childId] = childSize;
                    }
                }

                // 3. 使用尺寸计算器计算当前分支尺寸
                var calculatedSize = await _sizeCalculator.CalculateBranchSizeAsync(branch, nestedElements, childSizes);

                // 4. 应用最小尺寸约束
                var finalSize = ApplySizeConstraints(calculatedSize, branch);

                RecordPerformanceMetric($"BottomUpSize_{branch.Id}", DateTime.Now - startTime);
                return finalSize;
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"自底向上尺寸计算失败 (分支: {branch.Id}): {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 自顶向下更新位置
        /// </summary>
        private async Task<Dictionary<string, Point>> CalculateTopDownPositions(
            SFCBranchModel branch, 
            LayoutChangeContext context, 
            Size branchSize)
        {
            var positions = new Dictionary<string, Point>();
            var startTime = DateTime.Now;

            try
            {
                // 1. 设置当前分支位置（如果是根分支）
                if (string.IsNullOrEmpty(branch.ParentBranchId))
                {
                    positions[branch.Id] = branch.Position;
                }

                // 2. 计算子分支位置
                var childPositions = CalculateChildBranchPositions(branch, branchSize, context);
                foreach (var kvp in childPositions)
                {
                    positions[kvp.Key] = kvp.Value;
                }

                // 3. 递归计算子分支的子元素位置
                foreach (var childId in branch.ChildBranchIds)
                {
                    var childBranch = context.SFCModel?.Branches?.FirstOrDefault(b => b.Id == childId);
                    if (childBranch != null)
                    {
                        var childSize = childBranch.CalculatedSize;
                        var childSubPositions = await CalculateTopDownPositions(childBranch, context, childSize);
                        
                        foreach (var kvp in childSubPositions)
                        {
                            positions[kvp.Key] = kvp.Value;
                        }
                    }
                }

                RecordPerformanceMetric($"TopDownPositions_{branch.Id}", DateTime.Now - startTime);
                return positions;
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"自顶向下位置计算失败 (分支: {branch.Id}): {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 计算子分支位置
        /// </summary>
        private Dictionary<string, Point> CalculateChildBranchPositions(
            SFCBranchModel parentBranch, 
            Size parentSize, 
            LayoutChangeContext context)
        {
            var positions = new Dictionary<string, Point>();
            var currentY = parentBranch.Position.Y + _branchPadding;

            // 根据分支类型计算子分支布局
            switch (parentBranch.BranchType)
            {
                case SFCBranchType.Selection:
                    positions = CalculateSelectionBranchChildPositions(parentBranch, parentSize, context);
                    break;

                case SFCBranchType.Parallel:
                    positions = CalculateParallelBranchChildPositions(parentBranch, parentSize, context);
                    break;

                default:
                    // 默认垂直布局
                    positions = CalculateVerticalChildPositions(parentBranch, parentSize, context);
                    break;
            }

            return positions;
        }

        /// <summary>
        /// 计算选择分支的子分支位置
        /// </summary>
        private Dictionary<string, Point> CalculateSelectionBranchChildPositions(
            SFCBranchModel parentBranch, 
            Size parentSize, 
            LayoutChangeContext context)
        {
            var positions = new Dictionary<string, Point>();
            var startX = parentBranch.Position.X + _branchPadding;
            var currentY = parentBranch.Position.Y + _branchPadding + 20; // 为分支头部留空间

            foreach (var childId in parentBranch.ChildBranchIds)
            {
                var childBranch = context.SFCModel?.Branches?.FirstOrDefault(b => b.Id == childId);
                if (childBranch != null)
                {
                    positions[childId] = new Point(startX, currentY);
                    currentY += childBranch.CalculatedSize.Height + _nestedElementSpacing;
                }
            }

            return positions;
        }

        /// <summary>
        /// 计算并行分支的子分支位置
        /// </summary>
        private Dictionary<string, Point> CalculateParallelBranchChildPositions(
            SFCBranchModel parentBranch, 
            Size parentSize, 
            LayoutChangeContext context)
        {
            var positions = new Dictionary<string, Point>();
            var startY = parentBranch.Position.Y + _branchPadding + 20; // 为分支头部留空间
            var currentX = parentBranch.Position.X + _branchPadding;

            foreach (var childId in parentBranch.ChildBranchIds)
            {
                var childBranch = context.SFCModel?.Branches?.FirstOrDefault(b => b.Id == childId);
                if (childBranch != null)
                {
                    positions[childId] = new Point(currentX, startY);
                    currentX += childBranch.CalculatedSize.Width + _nestedElementSpacing;
                }
            }

            return positions;
        }

        /// <summary>
        /// 计算垂直布局的子分支位置
        /// </summary>
        private Dictionary<string, Point> CalculateVerticalChildPositions(
            SFCBranchModel parentBranch, 
            Size parentSize, 
            LayoutChangeContext context)
        {
            var positions = new Dictionary<string, Point>();
            var startX = parentBranch.Position.X + _branchPadding;
            var currentY = parentBranch.Position.Y + _branchPadding;

            foreach (var childId in parentBranch.ChildBranchIds)
            {
                var childBranch = context.SFCModel?.Branches?.FirstOrDefault(b => b.Id == childId);
                if (childBranch != null)
                {
                    positions[childId] = new Point(startX, currentY);
                    currentY += childBranch.CalculatedSize.Height + _nestedElementSpacing;
                }
            }

            return positions;
        }

        #endregion

        #region 辅助方法

        /// <summary>
        /// 计算连接点位置
        /// </summary>
        private Dictionary<int, Point> CalculateConnectionPointPositions(SFCBranchModel branch, Size branchSize)
        {
            var positions = new Dictionary<int, Point>();

            // 根据分支类型计算连接点位置
            switch (branch.BranchType)
            {
                case SFCBranchType.Selection:
                    // 选择分支：4个连接点
                    positions[0] = new Point(0, 10); // LeftTop
                    positions[1] = new Point(0, branchSize.Height - 10); // LeftBottom
                    positions[2] = new Point(branchSize.Width, 10); // RightTop
                    positions[3] = new Point(branchSize.Width, branchSize.Height - 10); // RightBottom
                    break;

                case SFCBranchType.Parallel:
                    // 并行分支：3个连接点
                    positions[0] = new Point(0, 10); // LeftTop
                    positions[1] = new Point(0, branchSize.Height / 2); // LeftParallel
                    positions[2] = new Point(branchSize.Width, branchSize.Height / 2); // RightParallel
                    break;

                default:
                    // 默认连接点
                    positions[0] = new Point(branchSize.Width / 2, 0); // Top
                    positions[1] = new Point(branchSize.Width / 2, branchSize.Height); // Bottom
                    break;
            }

            return positions;
        }

        /// <summary>
        /// 应用尺寸约束
        /// </summary>
        private Size ApplySizeConstraints(Size calculatedSize, SFCBranchModel branch)
        {
            var width = Math.Max(calculatedSize.Width, _minimumBranchWidth);
            var height = Math.Max(calculatedSize.Height, _minimumBranchHeight);

            // 根据分支类型应用特定约束
            switch (branch.BranchType)
            {
                case SFCBranchType.Selection:
                    width = Math.Max(width, 182); // 选择分支最小宽度
                    height = Math.Max(height, 50); // 选择分支最小高度
                    break;

                case SFCBranchType.Parallel:
                    width = Math.Max(width, 200); // 并行分支最小宽度
                    height = Math.Max(height, 50); // 并行分支最小高度
                    break;
            }

            return new Size(width, height);
        }

        /// <summary>
        /// 解决布局冲突
        /// </summary>
        private async Task<List<ConflictResolution>> ResolveLayoutConflicts(
            SFCBranchModel branch, 
            LayoutChangeContext context, 
            Size branchSize)
        {
            var resolutions = new List<ConflictResolution>();

            // 检测空间冲突
            var conflicts = _nestingDetector.HasNestedConflicts(branch, context.SFCModel);
            if (conflicts)
            {
                // 实现冲突解决逻辑
                var resolution = new ConflictResolution
                {
                    ConflictType = "SpatialOverlap",
                    Description = "检测到空间重叠冲突",
                    Resolution = "调整分支尺寸和位置",
                    AppliedAt = DateTime.Now
                };
                resolutions.Add(resolution);
            }

            return resolutions;
        }

        /// <summary>
        /// 应用布局结果到分支模型
        /// </summary>
        private void ApplyLayoutResultToBranch(SFCBranchModel branch, BranchLayoutResult layoutResult)
        {
            // 更新分支尺寸和位置
            branch.UpdateCalculatedSize(layoutResult.CalculatedSize, layoutResult.CalculationTime);

            // 更新布局状态
            branch.LastLayoutCalculation = DateTime.Now;
            branch.IsLayoutDirty = false;

            // 更新连接点偏移
            if (layoutResult.ConnectPointPositions != null)
            {
                // 这里可以更新连接点位置信息
                // 具体实现取决于连接点管理系统
            }
        }

        /// <summary>
        /// 生成布局缓存键
        /// </summary>
        private LayoutCacheKey GenerateLayoutCacheKey(SFCBranchModel branch, LayoutChangeContext context)
        {
            return new LayoutCacheKey
            {
                BranchId = branch.Id,
                Version = branch.LayoutVersion,
                NestedElementsHash = CalculateChildrenHash(branch).GetHashCode(),
                BranchPropertiesHash = CalculateContextHash(context).GetHashCode()
            };
        }

        /// <summary>
        /// 计算子分支哈希
        /// </summary>
        private string CalculateChildrenHash(SFCBranchModel branch)
        {
            var childIds = string.Join(",", branch.ChildBranchIds.OrderBy(id => id));
            return childIds.GetHashCode().ToString();
        }

        /// <summary>
        /// 计算上下文哈希
        /// </summary>
        private string CalculateContextHash(LayoutChangeContext context)
        {
            // 简化的上下文哈希计算
            return context.ChangeType.ToString().GetHashCode().ToString();
        }

        /// <summary>
        /// 获取父分支
        /// </summary>
        private SFCBranchModel? GetParentBranch(SFCBranchModel branch)
        {
            // 这里需要从上下文或服务中获取父分支
            // 具体实现取决于分支管理系统
            return null;
        }

        /// <summary>
        /// 按层次排序分支
        /// </summary>
        private List<SFCBranchModel> SortBranchesByHierarchy(IEnumerable<SFCBranchModel> branches)
        {
            var branchList = branches.ToList();
            var sorted = new List<SFCBranchModel>();
            var processed = new HashSet<string>();

            // 先处理没有父分支的根分支
            var rootBranches = branchList.Where(b => string.IsNullOrEmpty(b.ParentBranchId)).ToList();
            foreach (var root in rootBranches)
            {
                AddBranchAndChildren(root, branchList, sorted, processed);
            }

            // 处理剩余的分支
            foreach (var branch in branchList.Where(b => !processed.Contains(b.Id)))
            {
                sorted.Add(branch);
            }

            return sorted;
        }

        /// <summary>
        /// 递归添加分支及其子分支
        /// </summary>
        private void AddBranchAndChildren(
            SFCBranchModel branch, 
            List<SFCBranchModel> allBranches, 
            List<SFCBranchModel> sorted, 
            HashSet<string> processed)
        {
            if (processed.Contains(branch.Id)) return;

            // 先添加子分支
            foreach (var childId in branch.ChildBranchIds)
            {
                var childBranch = allBranches.FirstOrDefault(b => b.Id == childId);
                if (childBranch != null)
                {
                    AddBranchAndChildren(childBranch, allBranches, sorted, processed);
                }
            }

            // 再添加当前分支
            sorted.Add(branch);
            processed.Add(branch.Id);
        }

        /// <summary>
        /// 计算单个分支布局
        /// </summary>
        private async Task<KeyValuePair<string, BranchLayoutResult>> CalculateSingleBranchLayoutAsync(
            SFCBranchModel branch, 
            LayoutChangeContext context)
        {
            var result = await CalculateLayoutAsync(branch, context);
            return new KeyValuePair<string, BranchLayoutResult>(branch.Id, result);
        }

        /// <summary>
        /// 计算水平线扩展长度
        /// </summary>
        private double CalculateHorizontalLineExtension(SFCBranchModel branch, List<NestedElementInfo> nestedElements)
        {
            return _sizeCalculator.CalculateHorizontalLineExtension(
                branch.ChildBranchIds.Count, 
                branch.NestingLevel);
        }

        /// <summary>
        /// 记录性能指标
        /// </summary>
        private void RecordPerformanceMetric(string operation, TimeSpan duration)
        {
            _performanceMetrics[operation] = duration;
        }

        #endregion

        #region 公共属性

        /// <summary>
        /// 获取性能指标
        /// </summary>
        public IReadOnlyDictionary<string, TimeSpan> PerformanceMetrics => _performanceMetrics;

        #endregion
    }

    #region 辅助数据结构

    /// <summary>
    /// 冲突解决信息
    /// </summary>
    public class ConflictResolution
    {
        public string ConflictType { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Resolution { get; set; } = string.Empty;
        public DateTime AppliedAt { get; set; }
    }

    #endregion
}