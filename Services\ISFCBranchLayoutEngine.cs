using System.Threading.Tasks;
using PC_Control2.Demo.Models;

namespace PC_Control2.Demo.Services
{
    /// <summary>
    /// SFC分支布局引擎接口
    /// </summary>
    public interface ISFCBranchLayoutEngine
    {
        /// <summary>
        /// 计算分支布局
        /// </summary>
        Task<BranchLayoutResult> CalculateLayoutAsync(SFCBranchModel branch, LayoutChangeContext context);
    }

    /// <summary>
    /// 分支布局结果
    /// </summary>
    public class BranchLayoutResult
    {
        public string BranchId { get; set; } = string.Empty;
        public bool IsValid { get; set; }
        public System.Windows.Size CalculatedSize { get; set; }
        public System.Windows.Point CalculatedPosition { get; set; }
        public string ErrorMessage { get; set; } = string.Empty;
        public LayoutPerformanceInfo PerformanceInfo { get; set; } = new LayoutPerformanceInfo();
        
        // 新增：位置调整信息
        public List<ElementAdjustment> PositionAdjustments { get; set; } = new();
        public double TotalAdjustmentOffset { get; set; }
    }

    /// <summary>
    /// 布局变更上下文
    /// </summary>
    public class LayoutChangeContext
    {
        public LayoutChangeType ChangeType { get; set; }
        public SFCModel SFCContext { get; set; }
        public object ChangedElement { get; set; }
    }

    /// <summary>
    /// 布局性能信息
    /// </summary>
    public class LayoutPerformanceInfo
    {
        public double CalculationTimeMs { get; set; }
        public int ProcessedElementCount { get; set; }
    }
}