using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using PC_Control2.Demo.Models;

namespace PC_Control2.Demo.Services
{
    /// <summary>
    /// SFC元素位置调整器 - 正确的包络算法实现
    /// 通过调整元素位置来实现包络效果，而不是修改分支视图
    /// </summary>
    public class SFCElementPositionAdjuster
    {
        /// <summary>
        /// 应用包络调整 - 调整后续元素的位置来为嵌套内容让出空间
        /// </summary>
        public PositionAdjustmentResult ApplyEnvelopeAdjustment(
            SFCModel sfcModel,
            SFCStepModel parentStep,
            SFCBranchModel parentBranch,
            double nestedContentWidth)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"[位置调整] 🎯 开始应用包络调整");
                System.Diagnostics.Debug.WriteLine($"  父步骤: {parentStep?.Name}, 嵌套内容宽度: {nestedContentWidth}px");

                var result = new PositionAdjustmentResult();

                // 1. 找到需要调整位置的元素
                var elementsToAdjust = FindElementsToAdjust(sfcModel, parentStep, parentBranch);
                
                System.Diagnostics.Debug.WriteLine($"[位置调整] 📋 找到 {elementsToAdjust.Count} 个需要调整的元素");

                // 2. 计算需要的位移量
                var adjustmentOffset = CalculateAdjustmentOffset(nestedContentWidth);
                
                System.Diagnostics.Debug.WriteLine($"[位置调整] 📏 计算位移量: {adjustmentOffset}px");

                // 3. 应用位置调整
                foreach (var element in elementsToAdjust)
                {
                    var oldPosition = GetElementPosition(element);
                    var newPosition = new Point(oldPosition.X + adjustmentOffset, oldPosition.Y);
                    
                    SetElementPosition(element, newPosition);
                    
                    result.AdjustedElements.Add(new ElementAdjustment
                    {
                        Element = element,
                        OldPosition = oldPosition,
                        NewPosition = newPosition,
                        AdjustmentOffset = adjustmentOffset
                    });

                    System.Diagnostics.Debug.WriteLine($"[位置调整] 📐 {GetElementName(element)}: {oldPosition} -> {newPosition}");
                }

                result.IsSuccessful = true;
                result.TotalAdjustmentOffset = adjustmentOffset;

                System.Diagnostics.Debug.WriteLine($"[位置调整] ✅ 包络调整完成，调整了 {result.AdjustedElements.Count} 个元素");

                return result;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[位置调整] ❌ 异常: {ex.Message}");
                return new PositionAdjustmentResult
                {
                    IsSuccessful = false,
                    ErrorMessage = ex.Message
                };
            }
        }

        /// <summary>
        /// 找到需要调整位置的元素
        /// </summary>
        private List<object> FindElementsToAdjust(SFCModel sfcModel, SFCStepModel parentStep, SFCBranchModel parentBranch)
        {
            var elementsToAdjust = new List<object>();

            // 找到父步骤在分支中的位置
            var parentStepIndex = FindStepIndexInBranch(parentBranch, parentStep);
            if (parentStepIndex == -1)
            {
                System.Diagnostics.Debug.WriteLine($"[位置调整] ⚠️ 未找到父步骤在分支中的位置");
                return elementsToAdjust;
            }

            // 找到该分支中父步骤之后的所有元素
            var branchElements = GetBranchElements(parentBranch);
            for (int i = parentStepIndex + 1; i < branchElements.Count; i++)
            {
                elementsToAdjust.Add(branchElements[i]);
            }

            // 还需要调整分支汇聚后的所有元素
            var convergencePoint = FindBranchConvergencePoint(sfcModel, parentBranch);
            if (convergencePoint != null)
            {
                var subsequentElements = FindElementsAfterConvergence(sfcModel, convergencePoint);
                elementsToAdjust.AddRange(subsequentElements);
            }

            return elementsToAdjust;
        }

        /// <summary>
        /// 计算调整偏移量
        /// </summary>
        private double CalculateAdjustmentOffset(double nestedContentWidth)
        {
            // 嵌套内容宽度 + 额外间距
            const double EXTRA_SPACING = 20; // 额外间距
            return nestedContentWidth + EXTRA_SPACING;
        }

        /// <summary>
        /// 获取元素位置
        /// </summary>
        private Point GetElementPosition(object element)
        {
            return element switch
            {
                SFCStepModel step => step.Position,
                SFCTransitionModel transition => transition.Position,
                SFCBranchModel branch => branch.Position,
                _ => new Point(0, 0)
            };
        }

        /// <summary>
        /// 设置元素位置
        /// </summary>
        private void SetElementPosition(object element, Point position)
        {
            switch (element)
            {
                case SFCStepModel step:
                    step.Position = position;
                    break;
                case SFCTransitionModel transition:
                    transition.Position = position;
                    break;
                case SFCBranchModel branch:
                    branch.Position = position;
                    break;
            }
        }

        /// <summary>
        /// 获取元素名称
        /// </summary>
        private string GetElementName(object element)
        {
            return element switch
            {
                SFCStepModel step => $"步骤({step.Name})",
                SFCTransitionModel transition => $"转换({transition.Name})",
                SFCBranchModel branch => $"分支({branch.BranchType})",
                _ => "未知元素"
            };
        }

        /// <summary>
        /// 找到步骤在分支中的索引
        /// </summary>
        private int FindStepIndexInBranch(SFCBranchModel branch, SFCStepModel step)
        {
            // 这里需要根据实际的分支结构来实现
            // 暂时返回模拟值
            return 1; // 假设父步骤在分支中的索引为1
        }

        /// <summary>
        /// 获取分支中的所有元素
        /// </summary>
        private List<object> GetBranchElements(SFCBranchModel branch)
        {
            // 这里需要根据实际的分支结构来实现
            // 暂时返回空列表
            return new List<object>();
        }

        /// <summary>
        /// 找到分支汇聚点
        /// </summary>
        private object FindBranchConvergencePoint(SFCModel sfcModel, SFCBranchModel branch)
        {
            // 这里需要根据实际的SFC结构来实现
            // 暂时返回null
            return null;
        }

        /// <summary>
        /// 找到汇聚点之后的所有元素
        /// </summary>
        private List<object> FindElementsAfterConvergence(SFCModel sfcModel, object convergencePoint)
        {
            // 这里需要根据实际的SFC结构来实现
            // 暂时返回空列表
            return new List<object>();
        }
    }

    /// <summary>
    /// 位置调整结果
    /// </summary>
    public class PositionAdjustmentResult
    {
        public bool IsSuccessful { get; set; }
        public string ErrorMessage { get; set; } = string.Empty;
        public double TotalAdjustmentOffset { get; set; }
        public List<ElementAdjustment> AdjustedElements { get; set; } = new();
    }

    /// <summary>
    /// 元素调整信息
    /// </summary>
    public class ElementAdjustment
    {
        public object Element { get; set; } = null!;
        public Point OldPosition { get; set; }
        public Point NewPosition { get; set; }
        public double AdjustmentOffset { get; set; }
    }
}