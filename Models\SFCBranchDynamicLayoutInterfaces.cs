using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Windows;

namespace PC_Control2.Demo.Models
{
    #region 核心接口定义

    /// <summary>
    /// 分支布局引擎核心接口
    /// 负责分支的动态布局计算和管理
    /// </summary>
    public interface ISFCBranchLayoutEngine
    {
        /// <summary>
        /// 异步计算分支布局
        /// </summary>
        /// <param name="branch">要计算布局的分支</param>
        /// <param name="context">布局计算上下文</param>
        /// <returns>布局计算结果</returns>
        Task<BranchLayoutResult> CalculateLayoutAsync(SFCBranchModel branch, LayoutChangeContext context);

        /// <summary>
        /// 检查是否需要更新布局
        /// </summary>
        /// <param name="branch">分支模型</param>
        /// <param name="context">变化上下文</param>
        /// <returns>是否需要更新</returns>
        bool IsLayoutUpdateNeeded(SFCBranchModel branch, LayoutChangeContext context);

        /// <summary>
        /// 使布局缓存失效
        /// </summary>
        /// <param name="branch">分支模型</param>
        void InvalidateLayout(SFCBranchModel branch);

        /// <summary>
        /// 批量计算多个分支的布局
        /// </summary>
        /// <param name="branches">分支列表</param>
        /// <param name="context">布局计算上下文</param>
        /// <returns>批量布局计算结果</returns>
        Task<Dictionary<string, BranchLayoutResult>> CalculateBatchLayoutAsync(
            IEnumerable<SFCBranchModel> branches, LayoutChangeContext context);
    }

    /// <summary>
    /// 嵌套检测器接口
    /// 负责检测和分析分支内的嵌套元素
    /// </summary>
    public interface ISFCNestingDetector
    {
        /// <summary>
        /// 检测分支内的嵌套元素
        /// </summary>
        /// <param name="branch">分支模型</param>
        /// <param name="context">SFC模型上下文</param>
        /// <returns>嵌套元素信息列表</returns>
        List<NestedElementInfo> DetectNestedElements(SFCBranchModel branch, SFCModel context);

        /// <summary>
        /// 计算分支的嵌套深度
        /// </summary>
        /// <param name="branch">分支模型</param>
        /// <param name="context">SFC模型上下文</param>
        /// <returns>嵌套深度</returns>
        int CalculateNestingDepth(SFCBranchModel branch, SFCModel context);

        /// <summary>
        /// 检查是否存在嵌套冲突
        /// </summary>
        /// <param name="branch">分支模型</param>
        /// <param name="context">SFC模型上下文</param>
        /// <returns>是否存在冲突</returns>
        bool HasNestedConflicts(SFCBranchModel branch, SFCModel context);

        /// <summary>
        /// 获取分支的所有祖先分支
        /// </summary>
        /// <param name="branch">分支模型</param>
        /// <param name="context">SFC模型上下文</param>
        /// <returns>祖先分支列表</returns>
        List<SFCBranchModel> GetAncestorBranches(SFCBranchModel branch, SFCModel context);

        /// <summary>
        /// 获取分支的所有后代分支
        /// </summary>
        /// <param name="branch">分支模型</param>
        /// <param name="context">SFC模型上下文</param>
        /// <returns>后代分支列表</returns>
        List<SFCBranchModel> GetDescendantBranches(SFCBranchModel branch, SFCModel context);
    }

    /// <summary>
    /// 分支尺寸计算器接口
    /// 负责计算分支的动态尺寸
    /// </summary>
    public interface ISFCBranchSizeCalculator
    {
        /// <summary>
        /// 异步计算分支尺寸
        /// </summary>
        /// <param name="branch">分支模型</param>
        /// <param name="nestedElements">嵌套元素信息</param>
        /// <param name="childSizes">子分支尺寸字典</param>
        /// <returns>计算得出的尺寸</returns>
        Task<Size> CalculateBranchSizeAsync(SFCBranchModel branch, List<NestedElementInfo> nestedElements, Dictionary<string, Size> childSizes);

        /// <summary>
        /// 计算分支的最小尺寸
        /// </summary>
        /// <param name="branch">分支模型</param>
        /// <param name="nestedElements">嵌套元素信息</param>
        /// <returns>最小尺寸</returns>
        Size CalculateMinimumSize(SFCBranchModel branch, List<NestedElementInfo> nestedElements);

        /// <summary>
        /// 计算分支的最优尺寸
        /// </summary>
        /// <param name="branch">分支模型</param>
        /// <param name="nestedElements">嵌套元素信息</param>
        /// <returns>最优尺寸</returns>
        Size CalculateOptimalSize(SFCBranchModel branch, List<NestedElementInfo> nestedElements);

        /// <summary>
        /// 计算连接点位置
        /// </summary>
        /// <param name="branchSize">分支尺寸</param>
        /// <param name="branchType">分支类型</param>
        /// <returns>连接点位置字典</returns>
        Dictionary<int, Point> CalculateConnectPointPositions(Size branchSize, SFCBranchType branchType);

        /// <summary>
        /// 计算水平线扩展长度
        /// </summary>
        /// <param name="childBranchCount">子分支数量</param>
        /// <param name="nestingLevel">嵌套层级</param>
        /// <returns>扩展长度</returns>
        double CalculateHorizontalLineExtension(int childBranchCount, int nestingLevel);

        /// <summary>
        /// 计算子分支占用的空间
        /// </summary>
        /// <param name="childBranches">子分支列表</param>
        /// <param name="branchType">父分支类型</param>
        /// <returns>子分支占用的总空间</returns>
        Size CalculateChildrenSpace(List<BranchLayoutResult> childBranches, SFCBranchType branchType);
    }

    /// <summary>
    /// 分支层次管理器接口
    /// 负责管理分支的树形结构和层次关系
    /// </summary>
    public interface ISFCBranchHierarchyManager
    {
        /// <summary>
        /// 添加嵌套分支
        /// </summary>
        /// <param name="parentBranchId">父分支ID</param>
        /// <param name="newBranch">新分支</param>
        /// <param name="insertIndex">插入位置索引</param>
        void AddNestedBranch(string parentBranchId, SFCBranchModel newBranch, int insertIndex = -1);

        /// <summary>
        /// 移除嵌套分支
        /// </summary>
        /// <param name="branchId">分支ID</param>
        void RemoveNestedBranch(string branchId);

        /// <summary>
        /// 获取分支的祖先ID列表
        /// </summary>
        /// <param name="branchId">分支ID</param>
        /// <returns>祖先ID列表</returns>
        List<string> GetAncestorBranchIds(string branchId);

        /// <summary>
        /// 获取分支的后代ID列表
        /// </summary>
        /// <param name="branchId">分支ID</param>
        /// <returns>后代ID列表</returns>
        List<string> GetDescendantBranchIds(string branchId);

        /// <summary>
        /// 获取分支系统
        /// </summary>
        /// <param name="branchId">分支ID</param>
        /// <returns>分支系统</returns>
        BranchSystem? GetBranchSystem(string branchId);

        /// <summary>
        /// 标记需要级联更新
        /// </summary>
        /// <param name="branchId">分支ID</param>
        void MarkCascadeUpdateNeeded(string branchId);
    }

    /// <summary>
    /// 布局缓存管理器接口
    /// 负责布局计算结果的缓存和失效管理
    /// </summary>
    public interface ILayoutCacheManager
    {
        /// <summary>
        /// 获取缓存的布局结果
        /// </summary>
        /// <param name="branchId">分支ID</param>
        /// <param name="key">缓存键</param>
        /// <returns>缓存的布局结果</returns>
        CachedLayoutResult? GetCachedLayout(string branchId, LayoutCacheKey key);

        /// <summary>
        /// 缓存布局结果
        /// </summary>
        /// <param name="branchId">分支ID</param>
        /// <param name="key">缓存键</param>
        /// <param name="result">布局结果</param>
        void CacheLayout(string branchId, LayoutCacheKey key, BranchLayoutResult result);

        /// <summary>
        /// 使缓存失效
        /// </summary>
        /// <param name="branchId">分支ID</param>
        /// <param name="cascadeToRelated">是否级联到相关分支</param>
        void InvalidateCache(string branchId, bool cascadeToRelated = true);

        /// <summary>
        /// 清理过期缓存
        /// </summary>
        void CleanupExpiredCache();

        /// <summary>
        /// 获取缓存统计信息
        /// </summary>
        /// <returns>缓存统计</returns>
        CacheStatistics GetCacheStatistics();
    }

    #endregion

    #region 数据结构定义

    /// <summary>
    /// 分支系统
    /// 表示一个完整的分支层次结构
    /// </summary>
    public class BranchSystem
    {
        /// <summary>
        /// 根分支ID
        /// </summary>
        public string RootBranchId { get; set; } = string.Empty;

        /// <summary>
        /// 系统中所有分支的层次节点
        /// </summary>
        public Dictionary<string, BranchHierarchyNode> BranchNodes { get; set; } = new();

        /// <summary>
        /// 系统的最大嵌套深度
        /// </summary>
        public int MaxNestingDepth { get; set; } = 0;

        /// <summary>
        /// 系统的总分支数量
        /// </summary>
        public int TotalBranchCount => BranchNodes.Count;

        /// <summary>
        /// 系统是否需要重新计算布局
        /// </summary>
        public bool NeedsLayoutRecalculation { get; set; } = false;
    }

    /// <summary>
    /// 分支层次节点
    /// </summary>
    public class BranchHierarchyNode
    {
        /// <summary>
        /// 分支ID
        /// </summary>
        public string BranchId { get; set; } = string.Empty;

        /// <summary>
        /// 父分支ID
        /// </summary>
        public string? ParentBranchId { get; set; }

        /// <summary>
        /// 子分支ID列表
        /// </summary>
        public List<string> ChildBranchIds { get; set; } = new();

        /// <summary>
        /// 嵌套层级
        /// </summary>
        public int NestingLevel { get; set; } = 0;

        /// <summary>
        /// 在父分支中的索引
        /// </summary>
        public int IndexInParent { get; set; } = 0;

        /// <summary>
        /// 分支空间信息
        /// </summary>
        public BranchSpatialInfo SpatialInfo { get; set; } = new();
    }

    /// <summary>
    /// 分支空间信息
    /// </summary>
    public class BranchSpatialInfo
    {
        /// <summary>
        /// 元素ID
        /// </summary>
        public string ElementId { get; set; } = string.Empty;

        /// <summary>
        /// 绝对位置
        /// </summary>
        public Point Position { get; set; }

        /// <summary>
        /// 元素大小
        /// </summary>
        public Size Size { get; set; }

        /// <summary>
        /// 在父分支中的相对位置
        /// </summary>
        public Point RelativePosition { get; set; }

        /// <summary>
        /// 实际占用空间（包含子分支）
        /// </summary>
        public Size OccupiedSpace { get; set; }

        /// <summary>
        /// 最小空间需求
        /// </summary>
        public Size MinimumSpace { get; set; }

        /// <summary>
        /// 计算得出的分支尺寸
        /// </summary>
        public Size CalculatedSize { get; set; }

        /// <summary>
        /// 连接点位置偏移量
        /// </summary>
        public Dictionary<int, Point> ConnectPointOffsets { get; set; } = new();
    }

    /// <summary>
    /// 嵌套元素信息
    /// </summary>
    public class NestedElementInfo
    {
        /// <summary>
        /// 元素ID
        /// </summary>
        public string ElementId { get; set; } = string.Empty;

        /// <summary>
        /// 元素类型
        /// </summary>
        public SFCElementType ElementType { get; set; }

        /// <summary>
        /// 空间信息
        /// </summary>
        public BranchSpatialInfo SpatialInfo { get; set; } = new();

        /// <summary>
        /// 在父分支中的相对位置
        /// </summary>
        public Point RelativePosition { get; set; }

        /// <summary>
        /// 元素尺寸
        /// </summary>
        public Size ElementSize { get; set; }

        /// <summary>
        /// 嵌套层级
        /// </summary>
        public int NestingLevel { get; set; }

        /// <summary>
        /// 在同级中的索引
        /// </summary>
        public int IndexInLevel { get; set; }

        /// <summary>
        /// 是否为关键路径元素
        /// </summary>
        public bool IsCriticalPath { get; set; }
    }

    /// <summary>
    /// 分支布局结果
    /// </summary>
    public class BranchLayoutResult
    {
        /// <summary>
        /// 分支ID
        /// </summary>
        public string BranchId { get; set; } = string.Empty;

        /// <summary>
        /// 计算得出的尺寸
        /// </summary>
        public Size CalculatedSize { get; set; }

        /// <summary>
        /// 连接点位置字典
        /// </summary>
        public Dictionary<int, Point> ConnectPointPositions { get; set; } = new();

        /// <summary>
        /// 嵌套元素列表
        /// </summary>
        public List<NestedElementInfo> NestedElements { get; set; } = new();

        /// <summary>
        /// 计算耗时
        /// </summary>
        public TimeSpan CalculationTime { get; set; }

        /// <summary>
        /// 是否有效
        /// </summary>
        public bool IsValid { get; set; } = true;

        /// <summary>
        /// 水平线扩展长度
        /// </summary>
        public double HorizontalLineExtension { get; set; }

        /// <summary>
        /// 布局计算的时间戳
        /// </summary>
        public DateTime Timestamp { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 布局变化上下文
    /// </summary>
    public class LayoutChangeContext
    {
        /// <summary>
        /// SFC模型
        /// </summary>
        public SFCModel SFCModel { get; set; }

        /// <summary>
        /// 变化类型
        /// </summary>
        public LayoutChangeType ChangeType { get; set; }

        /// <summary>
        /// 触发变化的元素ID
        /// </summary>
        public string? TriggerElementId { get; set; }

        /// <summary>
        /// 变化的详细信息
        /// </summary>
        public Dictionary<string, object> ChangeDetails { get; set; } = new();

        /// <summary>
        /// 是否强制重新计算
        /// </summary>
        public bool ForceRecalculation { get; set; } = false;

        /// <summary>
        /// 构造函数
        /// </summary>
        public LayoutChangeContext(SFCModel sfcModel, LayoutChangeType changeType)
        {
            SFCModel = sfcModel;
            ChangeType = changeType;
        }

        /// <summary>
        /// 获取分支模型
        /// </summary>
        /// <param name="branchId">分支ID</param>
        /// <returns>分支模型</returns>
        public SFCBranchModel? GetBranch(string branchId)
        {
            return SFCModel.Branches.FirstOrDefault(b => b.Id == branchId);
        }
    }

    /// <summary>
    /// 布局变化类型
    /// </summary>
    public enum LayoutChangeType
    {
        /// <summary>
        /// 子分支添加
        /// </summary>
        ChildBranchAdded,

        /// <summary>
        /// 子分支删除
        /// </summary>
        ChildBranchRemoved,

        /// <summary>
        /// 元素移动
        /// </summary>
        ElementMoved,

        /// <summary>
        /// 相关元素移动
        /// </summary>
        RelatedElementMoved,

        /// <summary>
        /// 分支属性变化
        /// </summary>
        BranchPropertyChanged,

        /// <summary>
        /// 手动重新计算
        /// </summary>
        ManualRecalculation,

        /// <summary>
        /// 连接变化
        /// </summary>
        ConnectionChanged
    }

    /// <summary>
    /// 布局缓存键
    /// </summary>
    public class LayoutCacheKey : IEquatable<LayoutCacheKey>
    {
        /// <summary>
        /// 分支ID
        /// </summary>
        public string BranchId { get; set; } = string.Empty;

        /// <summary>
        /// 嵌套元素的哈希值
        /// </summary>
        public int NestedElementsHash { get; set; }

        /// <summary>
        /// 分支属性的哈希值
        /// </summary>
        public int BranchPropertiesHash { get; set; }

        /// <summary>
        /// 版本号
        /// </summary>
        public int Version { get; set; }

        public bool Equals(LayoutCacheKey? other)
        {
            if (other == null) return false;
            return BranchId == other.BranchId &&
                   NestedElementsHash == other.NestedElementsHash &&
                   BranchPropertiesHash == other.BranchPropertiesHash &&
                   Version == other.Version;
        }

        public override bool Equals(object? obj)
        {
            return Equals(obj as LayoutCacheKey);
        }

        public override int GetHashCode()
        {
            return HashCode.Combine(BranchId, NestedElementsHash, BranchPropertiesHash, Version);
        }
    }

    /// <summary>
    /// 缓存的布局结果
    /// </summary>
    public class CachedLayoutResult
    {
        /// <summary>
        /// 分支ID
        /// </summary>
        public string BranchId { get; set; } = string.Empty;

        /// <summary>
        /// 缓存键
        /// </summary>
        public LayoutCacheKey Key { get; set; } = new();

        /// <summary>
        /// 布局结果
        /// </summary>
        public BranchLayoutResult Result { get; set; } = new();

        /// <summary>
        /// 缓存时间
        /// </summary>
        public DateTime CachedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 最后访问时间
        /// </summary>
        public DateTime LastAccessed { get; set; } = DateTime.Now;

        /// <summary>
        /// 是否过期
        /// </summary>
        public bool IsExpired => DateTime.Now - CachedAt > TimeSpan.FromMinutes(30);
    }

    /// <summary>
    /// 缓存统计信息
    /// </summary>
    /// <summary>
    /// 缓存统计信息
    /// </summary>
    public class CacheStatistics
    {
        /// <summary>
        /// 缓存项数量
        /// </summary>
        public int CacheCount { get; set; }

        /// <summary>
        /// 命中次数
        /// </summary>
        public int HitCount { get; set; }

        /// <summary>
        /// 未命中次数
        /// </summary>
        public int MissCount { get; set; }

        /// <summary>
        /// 过期次数
        /// </summary>
        public int ExpiredCount { get; set; }

        /// <summary>
        /// 命中率
        /// </summary>
        public double HitRate => HitCount + MissCount > 0 ? (double)HitCount / (HitCount + MissCount) : 0;
    }

    /// <summary>
    /// 嵌套检测结果
    /// </summary>
    public class NestingDetectionResult
    {
        /// <summary>
        /// 空间冲突列表
        /// </summary>
        public List<SpatialConflictInfo> SpatialConflicts { get; set; } = new();

        /// <summary>
        /// 嵌套元素列表
        /// </summary>
        public List<NestedElementInfo> NestedElements { get; set; } = new();

        /// <summary>
        /// 是否存在循环嵌套
        /// </summary>
        public bool HasCircularNesting { get; set; } = false;

        /// <summary>
        /// 循环嵌套链
        /// </summary>
        public List<string> CircularNestingChain { get; set; } = new();

        /// <summary>
        /// 最大嵌套深度
        /// </summary>
        public int MaxNestingDepth { get; set; } = 0;

        /// <summary>
        /// 总空间需求
        /// </summary>
        public Size TotalSpaceRequired { get; set; } = new Size(0, 0);
    }

    /// <summary>
    /// 空间冲突信息
    /// </summary>
    public class SpatialConflictInfo
    {
        /// <summary>
        /// 分支1 ID
        /// </summary>
        public string Branch1Id { get; set; } = string.Empty;

        /// <summary>
        /// 分支2 ID
        /// </summary>
        public string Branch2Id { get; set; } = string.Empty;

        /// <summary>
        /// 是否存在冲突
        /// </summary>
        public bool HasConflict { get; set; } = false;

        /// <summary>
        /// 冲突类型
        /// </summary>
        public SpatialConflictType ConflictType { get; set; } = SpatialConflictType.None;

        /// <summary>
        /// 重叠区域
        /// </summary>
        public Rect OverlapArea { get; set; } = Rect.Empty;

        /// <summary>
        /// 距离
        /// </summary>
        public double Distance { get; set; } = 0;

        /// <summary>
        /// 严重程度 (0-1)
        /// </summary>
        public double Severity { get; set; } = 0;
    }

    /// <summary>
    /// 空间冲突类型
    /// </summary>
    public enum SpatialConflictType
    {
        /// <summary>
        /// 无冲突
        /// </summary>
        None,

        /// <summary>
        /// 重叠
        /// </summary>
        Overlap,

        /// <summary>
        /// 距离过近
        /// </summary>
        TooClose,

        /// <summary>
        /// 完全包含
        /// </summary>
        FullyContained
    }



    #endregion

    #region 连接点同步器接口

    /// <summary>
    /// 连接点同步器接口
    /// 负责在分支尺寸变化后同步更新连接点位置
    /// </summary>
    public interface ISFCConnectionPointSynchronizer
    {
        /// <summary>
        /// 同步连接点位置
        /// </summary>
        Task<ConnectionPointSyncResult> SynchronizeConnectionPointsAsync(
            SFCBranchModel branch, 
            BranchLayoutResult layoutResult, 
            SFCModel context);

        /// <summary>
        /// 批量同步多个分支的连接点
        /// </summary>
        Task<List<ConnectionPointSyncResult>> BatchSynchronizeAsync(
            List<SFCBranchModel> branches, 
            Dictionary<string, BranchLayoutResult> layoutResults, 
            SFCModel context);

        /// <summary>
        /// 检测连接点位置冲突
        /// </summary>
        List<ConnectionPointConflict> DetectConnectionPointConflicts(
            SFCBranchModel branch, 
            SFCModel context);

        /// <summary>
        /// 解决连接点冲突
        /// </summary>
        Task<ConflictResolutionResult> ResolveConnectionPointConflictsAsync(
            List<ConnectionPointConflict> conflicts, 
            SFCModel context);

        /// <summary>
        /// 获取连接点位置历史记录
        /// </summary>
        Dictionary<string, List<ConnectionPointPositionHistory>> GetConnectionPointHistory(string branchId);
    }

    #endregion

    #region 连接点同步相关数据结构

    /// <summary>
    /// 连接点同步结果
    /// </summary>
    public class ConnectionPointSyncResult
    {
        /// <summary>
        /// 分支ID
        /// </summary>
        public string BranchId { get; set; } = string.Empty;

        /// <summary>
        /// 同步时间戳
        /// </summary>
        public DateTime SyncTimestamp { get; set; } = DateTime.Now;

        /// <summary>
        /// 更新的连接信息列表
        /// </summary>
        public List<ConnectionUpdateInfo> UpdatedConnections { get; set; } = new();

        /// <summary>
        /// 是否同步成功
        /// </summary>
        public bool IsSuccessful { get; set; } = true;

        /// <summary>
        /// 错误消息
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// 验证错误列表
        /// </summary>
        public List<string> ValidationErrors { get; set; } = new();
    }

    /// <summary>
    /// 连接更新信息
    /// </summary>
    public class ConnectionUpdateInfo
    {
        /// <summary>
        /// 分支ID
        /// </summary>
        public string BranchId { get; set; } = string.Empty;

        /// <summary>
        /// 连接点索引
        /// </summary>
        public int ConnectionPointIndex { get; set; }

        /// <summary>
        /// 旧位置
        /// </summary>
        public Point OldPosition { get; set; }

        /// <summary>
        /// 新位置
        /// </summary>
        public Point NewPosition { get; set; }

        /// <summary>
        /// 更新原因
        /// </summary>
        public string UpdateReason { get; set; } = string.Empty;
    }

    /// <summary>
    /// 连接点冲突信息
    /// </summary>
    public class ConnectionPointConflict
    {
        /// <summary>
        /// 冲突类型
        /// </summary>
        public ConnectionPointConflictType ConflictType { get; set; }

        /// <summary>
        /// 分支ID
        /// </summary>
        public string BranchId { get; set; } = string.Empty;

        /// <summary>
        /// 冲突的连接点列表
        /// </summary>
        public List<ConnectionPointInfo> ConflictingPoints { get; set; } = new();

        /// <summary>
        /// 冲突严重程度
        /// </summary>
        public ConflictSeverity Severity { get; set; }

        /// <summary>
        /// 冲突描述
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// 解决错误信息
        /// </summary>
        public string? ResolutionError { get; set; }
    }

    /// <summary>
    /// 连接点信息
    /// </summary>
    public class ConnectionPointInfo
    {
        /// <summary>
        /// 连接点索引
        /// </summary>
        public int Index { get; set; }

        /// <summary>
        /// 连接点位置
        /// </summary>
        public Point Position { get; set; }

        /// <summary>
        /// 所属分支ID
        /// </summary>
        public string BranchId { get; set; } = string.Empty;

        /// <summary>
        /// 连接点类型
        /// </summary>
        public ConnectionPointType Type { get; set; }
    }

    /// <summary>
    /// 冲突解决结果
    /// </summary>
    public class ConflictResolutionResult
    {
        /// <summary>
        /// 已解决的冲突列表
        /// </summary>
        public List<ConnectionPointConflict> ResolvedConflicts { get; set; } = new();

        /// <summary>
        /// 未解决的冲突列表
        /// </summary>
        public List<ConnectionPointConflict> UnresolvedConflicts { get; set; } = new();

        /// <summary>
        /// 应用的解决方案列表
        /// </summary>
        public List<ConflictSolution> AppliedSolutions { get; set; } = new();

        /// <summary>
        /// 是否成功
        /// </summary>
        public bool IsSuccessful { get; set; } = true;
    }

    /// <summary>
    /// 冲突解决方案
    /// </summary>
    public class ConflictSolution
    {
        /// <summary>
        /// 冲突ID
        /// </summary>
        public string ConflictId { get; set; } = string.Empty;

        /// <summary>
        /// 解决方案类型
        /// </summary>
        public ConflictSolutionType SolutionType { get; set; }

        /// <summary>
        /// 解决方案描述
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// 解决方案参数
        /// </summary>
        public Dictionary<string, object> Parameters { get; set; } = new();
    }

    /// <summary>
    /// 连接点位置历史记录
    /// </summary>
    public class ConnectionPointPositionHistory
    {
        /// <summary>
        /// 连接点索引
        /// </summary>
        public int Index { get; set; }

        /// <summary>
        /// 位置
        /// </summary>
        public Point Position { get; set; }

        /// <summary>
        /// 记录时间
        /// </summary>
        public DateTime Timestamp { get; set; } = DateTime.Now;

        /// <summary>
        /// 变化原因
        /// </summary>
        public string ChangeReason { get; set; } = string.Empty;
    }

    #endregion

    #region 连接点同步相关枚举

    /// <summary>
    /// 连接点冲突类型
    /// </summary>
    public enum ConnectionPointConflictType
    {
        /// <summary>
        /// 无冲突
        /// </summary>
        None,

        /// <summary>
        /// 位置重叠
        /// </summary>
        PositionOverlap,

        /// <summary>
        /// 连接线交叉
        /// </summary>
        ConnectionCrossing,

        /// <summary>
        /// 边界越界
        /// </summary>
        BoundaryViolation
    }

    /// <summary>
    /// 冲突严重程度
    /// </summary>
    public enum ConflictSeverity
    {
        /// <summary>
        /// 低
        /// </summary>
        Low,

        /// <summary>
        /// 中等
        /// </summary>
        Medium,

        /// <summary>
        /// 高
        /// </summary>
        High,

        /// <summary>
        /// 严重
        /// </summary>
        Critical
    }

    /// <summary>
    /// 连接点类型
    /// </summary>
    public enum ConnectionPointType
    {
        /// <summary>
        /// 输入
        /// </summary>
        Input,

        /// <summary>
        /// 输出
        /// </summary>
        Output,

        /// <summary>
        /// 双向
        /// </summary>
        Bidirectional
    }

    /// <summary>
    /// 冲突解决方案类型
    /// </summary>
    public enum ConflictSolutionType
    {
        /// <summary>
        /// 调整分支大小
        /// </summary>
        AdjustBranchSize,

        /// <summary>
        /// 重新路由连接
        /// </summary>
        RerouteConnection,

        /// <summary>
        /// 移动连接点
        /// </summary>
        MoveConnectionPoint,

        /// <summary>
        /// 调整分支位置
        /// </summary>
        AdjustBranchPosition
    }

    #endregion

    #region 性能监控接口

    /// <summary>
    /// SFC分支布局性能监控器接口
    /// 监控布局计算的性能指标，提供性能分析和优化建议
    /// </summary>
    public interface ISFCBranchLayoutPerformanceMonitor
    {
        /// <summary>
        /// 开始监控布局计算性能
        /// </summary>
        string StartLayoutCalculationMonitoring(string branchId, string operationType);

        /// <summary>
        /// 结束监控并记录性能指标
        /// </summary>
        PerformanceMetrics EndLayoutCalculationMonitoring(string monitoringId, LayoutCalculationResult result);

        /// <summary>
        /// 记录布局操作的详细信息
        /// </summary>
        void RecordLayoutOperation(string monitoringId, string operationName, TimeSpan duration, Dictionary<string, object>? additionalData = null);

        /// <summary>
        /// 获取性能统计信息
        /// </summary>
        PerformanceStatistics GetPerformanceStatistics(TimeSpan? timeRange = null);

        /// <summary>
        /// 获取性能瓶颈分析
        /// </summary>
        List<PerformanceBottleneck> AnalyzePerformanceBottlenecks(int topCount = 10);

        /// <summary>
        /// 获取性能优化建议
        /// </summary>
        List<PerformanceRecommendation> GetPerformanceRecommendations();

        /// <summary>
        /// 生成性能报告
        /// </summary>
        Task<PerformanceReport> GeneratePerformanceReportAsync(TimeSpan timeRange);
    }

    #endregion

    #region 性能监控数据结构

    /// <summary>
    /// 性能指标
    /// </summary>
    public class PerformanceMetrics
    {
        /// <summary>
        /// 分支ID
        /// </summary>
        public string BranchId { get; set; } = string.Empty;

        /// <summary>
        /// 操作类型
        /// </summary>
        public string OperationType { get; set; } = string.Empty;

        /// <summary>
        /// 监控ID
        /// </summary>
        public string MonitoringId { get; set; } = string.Empty;

        /// <summary>
        /// 开始时间
        /// </summary>
        public DateTime StartTime { get; set; }

        /// <summary>
        /// 结束时间
        /// </summary>
        public DateTime EndTime { get; set; }

        /// <summary>
        /// 执行时长
        /// </summary>
        public TimeSpan Duration { get; set; }

        /// <summary>
        /// 内存使用量
        /// </summary>
        public long MemoryUsage { get; set; }

        /// <summary>
        /// 处理的元素数量
        /// </summary>
        public int ElementCount { get; set; }

        /// <summary>
        /// 缓存命中次数
        /// </summary>
        public int CacheHitCount { get; set; }

        /// <summary>
        /// 缓存未命中次数
        /// </summary>
        public int CacheMissCount { get; set; }

        /// <summary>
        /// 性能状态
        /// </summary>
        public PerformanceStatus Status { get; set; }

        /// <summary>
        /// 性能评级
        /// </summary>
        public PerformanceRating PerformanceRating { get; set; }

        /// <summary>
        /// 错误消息
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// 操作记录列表
        /// </summary>
        public List<LayoutOperationRecord> Operations { get; set; } = new();
    }

    /// <summary>
    /// 布局操作记录
    /// </summary>
    public class LayoutOperationRecord
    {
        /// <summary>
        /// 操作名称
        /// </summary>
        public string OperationName { get; set; } = string.Empty;

        /// <summary>
        /// 执行时长
        /// </summary>
        public TimeSpan Duration { get; set; }

        /// <summary>
        /// 时间戳
        /// </summary>
        public DateTime Timestamp { get; set; }

        /// <summary>
        /// 附加数据
        /// </summary>
        public Dictionary<string, object> AdditionalData { get; set; } = new();
    }

    /// <summary>
    /// 布局计算结果
    /// </summary>
    public class LayoutCalculationResult
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool IsSuccessful { get; set; }

        /// <summary>
        /// 处理的元素数量
        /// </summary>
        public int ProcessedElementCount { get; set; }

        /// <summary>
        /// 缓存命中次数
        /// </summary>
        public int CacheHitCount { get; set; }

        /// <summary>
        /// 缓存未命中次数
        /// </summary>
        public int CacheMissCount { get; set; }

        /// <summary>
        /// 错误消息
        /// </summary>
        public string? ErrorMessage { get; set; }
    }

    /// <summary>
    /// 性能统计信息
    /// </summary>
    public class PerformanceStatistics
    {
        /// <summary>
        /// 总操作数
        /// </summary>
        public int TotalOperations { get; set; }

        /// <summary>
        /// 平均执行时长
        /// </summary>
        public TimeSpan AverageDuration { get; set; }

        /// <summary>
        /// 最小执行时长
        /// </summary>
        public TimeSpan MinDuration { get; set; }

        /// <summary>
        /// 最大执行时长
        /// </summary>
        public TimeSpan MaxDuration { get; set; }

        /// <summary>
        /// 平均元素数量
        /// </summary>
        public int AverageElementCount { get; set; }

        /// <summary>
        /// 慢操作数量
        /// </summary>
        public int SlowOperationsCount { get; set; }

        /// <summary>
        /// 缓存命中率
        /// </summary>
        public double CacheHitRate { get; set; }

        /// <summary>
        /// 性能评级分布
        /// </summary>
        public Dictionary<PerformanceRating, int> PerformanceRatingDistribution { get; set; } = new();

        /// <summary>
        /// 统计时间范围
        /// </summary>
        public TimeSpan TimeRange { get; set; }
    }

    /// <summary>
    /// 性能瓶颈
    /// </summary>
    public class PerformanceBottleneck
    {
        /// <summary>
        /// 瓶颈类型
        /// </summary>
        public BottleneckType BottleneckType { get; set; }

        /// <summary>
        /// 描述
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// 受影响的操作类型
        /// </summary>
        public string AffectedOperationType { get; set; } = string.Empty;

        /// <summary>
        /// 发生频率
        /// </summary>
        public int Frequency { get; set; }

        /// <summary>
        /// 平均执行时长
        /// </summary>
        public TimeSpan AverageDuration { get; set; }

        /// <summary>
        /// 缓存命中率
        /// </summary>
        public double CacheHitRate { get; set; }

        /// <summary>
        /// 严重程度
        /// </summary>
        public BottleneckSeverity Severity { get; set; }

        /// <summary>
        /// 优化建议
        /// </summary>
        public List<string> Recommendations { get; set; } = new();
    }

    /// <summary>
    /// 性能优化建议
    /// </summary>
    public class PerformanceRecommendation
    {
        /// <summary>
        /// 建议类型
        /// </summary>
        public RecommendationType Type { get; set; }

        /// <summary>
        /// 优先级
        /// </summary>
        public RecommendationPriority Priority { get; set; }

        /// <summary>
        /// 标题
        /// </summary>
        public string Title { get; set; } = string.Empty;

        /// <summary>
        /// 描述
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// 具体建议
        /// </summary>
        public List<string> Suggestions { get; set; } = new();
    }

    /// <summary>
    /// 性能报告
    /// </summary>
    public class PerformanceReport
    {
        /// <summary>
        /// 生成时间
        /// </summary>
        public DateTime GeneratedAt { get; set; }

        /// <summary>
        /// 统计时间范围
        /// </summary>
        public TimeSpan TimeRange { get; set; }

        /// <summary>
        /// 性能统计
        /// </summary>
        public PerformanceStatistics Statistics { get; set; } = new();

        /// <summary>
        /// 性能瓶颈列表
        /// </summary>
        public List<PerformanceBottleneck> Bottlenecks { get; set; } = new();

        /// <summary>
        /// 优化建议列表
        /// </summary>
        public List<PerformanceRecommendation> Recommendations { get; set; } = new();

        /// <summary>
        /// 报告摘要
        /// </summary>
        public string Summary { get; set; } = string.Empty;
    }

    #endregion

    #region 性能监控枚举

    /// <summary>
    /// 性能状态
    /// </summary>
    public enum PerformanceStatus
    {
        /// <summary>
        /// 进行中
        /// </summary>
        InProgress,

        /// <summary>
        /// 已完成
        /// </summary>
        Completed,

        /// <summary>
        /// 失败
        /// </summary>
        Failed
    }

    /// <summary>
    /// 性能评级
    /// </summary>
    public enum PerformanceRating
    {
        /// <summary>
        /// 优秀
        /// </summary>
        Excellent,

        /// <summary>
        /// 良好
        /// </summary>
        Good,

        /// <summary>
        /// 一般
        /// </summary>
        Fair,

        /// <summary>
        /// 较差
        /// </summary>
        Poor,

        /// <summary>
        /// 严重
        /// </summary>
        Critical
    }

    /// <summary>
    /// 瓶颈类型
    /// </summary>
    public enum BottleneckType
    {
        /// <summary>
        /// 慢操作
        /// </summary>
        SlowOperation,

        /// <summary>
        /// 低缓存命中率
        /// </summary>
        LowCacheHitRate,

        /// <summary>
        /// 高内存使用
        /// </summary>
        HighMemoryUsage,

        /// <summary>
        /// 频繁操作
        /// </summary>
        FrequentOperation
    }

    /// <summary>
    /// 瓶颈严重程度
    /// </summary>
    public enum BottleneckSeverity
    {
        /// <summary>
        /// 低
        /// </summary>
        Low,

        /// <summary>
        /// 中等
        /// </summary>
        Medium,

        /// <summary>
        /// 高
        /// </summary>
        High,

        /// <summary>
        /// 严重
        /// </summary>
        Critical
    }

    /// <summary>
    /// 建议类型
    /// </summary>
    public enum RecommendationType
    {
        /// <summary>
        /// 性能优化
        /// </summary>
        Performance,

        /// <summary>
        /// 缓存优化
        /// </summary>
        Cache,

        /// <summary>
        /// 算法优化
        /// </summary>
        Algorithm,

        /// <summary>
        /// 内存优化
        /// </summary>
        Memory
    }

    /// <summary>
    /// 建议优先级
    /// </summary>
    public enum RecommendationPriority
    {
        /// <summary>
        /// 低
        /// </summary>
        Low,

        /// <summary>
        /// 中等
        /// </summary>
        Medium,

        /// <summary>
        /// 高
        /// </summary>
        High,

        /// <summary>
        /// 严重
        /// </summary>
        Critical
    }

    #endregion
}