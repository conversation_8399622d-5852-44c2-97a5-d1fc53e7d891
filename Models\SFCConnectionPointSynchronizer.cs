using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;

namespace PC_Control2.Demo.Models
{
    /// <summary>
    /// SFC连接点同步器实现
    /// 负责在分支尺寸变化后同步更新连接点位置
    /// </summary>
    public class SFCConnectionPointSynchronizer : ISFCConnectionPointSynchronizer
    {
        #region 私有字段

        private readonly ISFCBranchSizeCalculator _sizeCalculator;
        private readonly Dictionary<string, Point> _lastKnownPositions;
        private readonly object _syncLock = new object();

        #endregion

        #region 构造函数

        public SFCConnectionPointSynchronizer(ISFCBranchSizeCalculator sizeCalculator)
        {
            _sizeCalculator = sizeCalculator ?? throw new ArgumentNullException(nameof(sizeCalculator));
            _lastKnownPositions = new Dictionary<string, Point>();
        }

        #endregion

        #region ISFCConnectionPointSynchronizer 实现

        /// <summary>
        /// 同步连接点位置
        /// </summary>
        public async Task<ConnectionPointSyncResult> SynchronizeConnectionPointsAsync(
            SFCBranchModel branch, 
            BranchLayoutResult layoutResult, 
            SFCModel context)
        {
            if (branch == null) throw new ArgumentNullException(nameof(branch));
            if (layoutResult == null) throw new ArgumentNullException(nameof(layoutResult));
            if (context == null) throw new ArgumentNullException(nameof(context));

            var syncResult = new ConnectionPointSyncResult
            {
                BranchId = branch.Id,
                SyncTimestamp = DateTime.Now,
                UpdatedConnections = new List<ConnectionUpdateInfo>(),
                IsSuccessful = true
            };

            try
            {
                // 1. 更新分支自身的连接点位置
                await UpdateBranchConnectionPoints(branch, layoutResult, syncResult);

                // 2. 更新相关连接线的位置
                await UpdateRelatedConnections(branch, context, syncResult);

                // 3. 级联更新子分支的连接点
                await CascadeUpdateChildBranches(branch, context, syncResult);

                // 4. 验证连接点位置的一致性
                ValidateConnectionPointConsistency(branch, syncResult);

                return syncResult;
            }
            catch (Exception ex)
            {
                syncResult.IsSuccessful = false;
                syncResult.ErrorMessage = $"连接点同步失败: {ex.Message}";
                return syncResult;
            }
        }

        /// <summary>
        /// 批量同步多个分支的连接点
        /// </summary>
        public async Task<List<ConnectionPointSyncResult>> BatchSynchronizeAsync(
            List<SFCBranchModel> branches, 
            Dictionary<string, BranchLayoutResult> layoutResults, 
            SFCModel context)
        {
            if (branches == null) throw new ArgumentNullException(nameof(branches));
            if (layoutResults == null) throw new ArgumentNullException(nameof(layoutResults));
            if (context == null) throw new ArgumentNullException(nameof(context));

            var results = new List<ConnectionPointSyncResult>();

            // 按照依赖关系排序分支（父分支优先）
            var sortedBranches = SortBranchesByDependency(branches);

            foreach (var branch in sortedBranches)
            {
                if (layoutResults.TryGetValue(branch.Id, out var layoutResult))
                {
                    var syncResult = await SynchronizeConnectionPointsAsync(branch, layoutResult, context);
                    results.Add(syncResult);
                }
            }

            return results;
        }

        /// <summary>
        /// 检测连接点位置冲突
        /// </summary>
        public List<ConnectionPointConflict> DetectConnectionPointConflicts(
            SFCBranchModel branch, 
            SFCModel context)
        {
            if (branch == null) throw new ArgumentNullException(nameof(branch));
            if (context == null) throw new ArgumentNullException(nameof(context));

            var conflicts = new List<ConnectionPointConflict>();

            // 获取分支的所有连接点
            var connectionPoints = GetBranchConnectionPoints(branch);

            // 检测重叠冲突
            for (int i = 0; i < connectionPoints.Count; i++)
            {
                for (int j = i + 1; j < connectionPoints.Count; j++)
                {
                    var point1 = connectionPoints[i];
                    var point2 = connectionPoints[j];

                    if (IsPositionConflict(point1.Position, point2.Position))
                    {
                        conflicts.Add(new ConnectionPointConflict
                        {
                            ConflictType = ConnectionPointConflictType.PositionOverlap,
                            BranchId = branch.Id,
                            ConflictingPoints = new List<ConnectionPointInfo> { point1, point2 },
                            Severity = ConflictSeverity.High,
                            Description = $"连接点 {point1.Index} 和 {point2.Index} 位置重叠"
                        });
                    }
                }
            }

            // 检测连接线交叉冲突
            DetectConnectionLineCrossings(branch, context, conflicts);

            return conflicts;
        }

        /// <summary>
        /// 解决连接点冲突
        /// </summary>
        public async Task<ConflictResolutionResult> ResolveConnectionPointConflictsAsync(
            List<ConnectionPointConflict> conflicts, 
            SFCModel context)
        {
            if (conflicts == null) throw new ArgumentNullException(nameof(conflicts));
            if (context == null) throw new ArgumentNullException(nameof(context));

            var resolutionResult = new ConflictResolutionResult
            {
                ResolvedConflicts = new List<ConnectionPointConflict>(),
                UnresolvedConflicts = new List<ConnectionPointConflict>(),
                AppliedSolutions = new List<ConflictSolution>(),
                IsSuccessful = true
            };

            foreach (var conflict in conflicts)
            {
                try
                {
                    var solution = await GenerateConflictSolution(conflict, context);
                    if (solution != null && await ApplyConflictSolution(solution, context))
                    {
                        resolutionResult.ResolvedConflicts.Add(conflict);
                        resolutionResult.AppliedSolutions.Add(solution);
                    }
                    else
                    {
                        resolutionResult.UnresolvedConflicts.Add(conflict);
                    }
                }
                catch (Exception ex)
                {
                    conflict.ResolutionError = ex.Message;
                    resolutionResult.UnresolvedConflicts.Add(conflict);
                }
            }

            resolutionResult.IsSuccessful = resolutionResult.UnresolvedConflicts.Count == 0;
            return resolutionResult;
        }

        /// <summary>
        /// 获取连接点位置历史记录
        /// </summary>
        public Dictionary<string, List<ConnectionPointPositionHistory>> GetConnectionPointHistory(string branchId)
        {
            // 实现连接点位置历史记录功能
            // 这里返回空字典，实际实现中应该从历史记录存储中获取
            return new Dictionary<string, List<ConnectionPointPositionHistory>>();
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 更新分支连接点位置
        /// </summary>
        private async Task UpdateBranchConnectionPoints(
            SFCBranchModel branch, 
            BranchLayoutResult layoutResult, 
            ConnectionPointSyncResult syncResult)
        {
            // 计算新的连接点位置
            var newPositions = _sizeCalculator.CalculateConnectPointPositions(
                layoutResult.CalculatedSize, 
                branch.BranchType);

            // 更新连接点位置
            foreach (var kvp in newPositions)
            {
                var index = kvp.Key;
                var newPosition = kvp.Value;

                // 记录位置变化
                var positionKey = $"{branch.Id}_{index}";
                if (_lastKnownPositions.TryGetValue(positionKey, out var oldPosition))
                {
                    if (oldPosition != newPosition)
                    {
                        syncResult.UpdatedConnections.Add(new ConnectionUpdateInfo
                        {
                            BranchId = branch.Id,
                            ConnectionPointIndex = index,
                            OldPosition = oldPosition,
                            NewPosition = newPosition,
                            UpdateReason = "分支尺寸变化"
                        });
                    }
                }

                // 更新位置记录
                lock (_syncLock)
                {
                    _lastKnownPositions[positionKey] = newPosition;
                }
            }

            await Task.CompletedTask;
        }

        /// <summary>
        /// 更新相关连接线位置
        /// </summary>
        private async Task UpdateRelatedConnections(
            SFCBranchModel branch, 
            SFCModel context, 
            ConnectionPointSyncResult syncResult)
        {
            // 查找与此分支相关的所有连接线
            var relatedConnections = context.Connections?.Where(c => 
                c.SourceId == branch.Id || c.TargetId == branch.Id).ToList() ?? new List<SFCConnectionModel>();

            foreach (var connection in relatedConnections)
            {
                // 更新连接线的端点位置
                await UpdateConnectionEndpoints(connection, context, syncResult);
            }
        }

        /// <summary>
        /// 级联更新子分支连接点
        /// </summary>
        private async Task CascadeUpdateChildBranches(
            SFCBranchModel branch, 
            SFCModel context, 
            ConnectionPointSyncResult syncResult)
        {
            foreach (var childBranchId in branch.ChildBranchIds)
            {
                var childBranch = context.Branches?.FirstOrDefault(b => b.Id == childBranchId);
                if (childBranch != null)
                {
                    // 递归更新子分支的连接点
                    // 这里需要重新计算子分支的布局
                    var childLayoutResult = new BranchLayoutResult
                    {
                        BranchId = childBranch.Id,
                        CalculatedSize = childBranch.CalculatedSize,
                        ConnectPointPositions = new Dictionary<int, Point>(),
                        IsValid = true,
                        Timestamp = DateTime.Now
                    };

                    await UpdateBranchConnectionPoints(childBranch, childLayoutResult, syncResult);
                }
            }
        }

        /// <summary>
        /// 验证连接点位置一致性
        /// </summary>
        private void ValidateConnectionPointConsistency(
            SFCBranchModel branch, 
            ConnectionPointSyncResult syncResult)
        {
            // 验证连接点位置是否合理
            var connectionPoints = GetBranchConnectionPoints(branch);
            
            foreach (var point in connectionPoints)
            {
                // 检查位置是否在分支边界内
                if (!IsPositionWithinBranchBounds(point.Position, branch))
                {
                    syncResult.ValidationErrors.Add($"连接点 {point.Index} 位置超出分支边界");
                }

                // 检查位置是否为有效坐标
                if (double.IsNaN(point.Position.X) || double.IsNaN(point.Position.Y))
                {
                    syncResult.ValidationErrors.Add($"连接点 {point.Index} 位置包含无效坐标");
                }
            }
        }

        /// <summary>
        /// 按依赖关系排序分支
        /// </summary>
        private List<SFCBranchModel> SortBranchesByDependency(List<SFCBranchModel> branches)
        {
            // 实现拓扑排序，确保父分支在子分支之前处理
            var sorted = new List<SFCBranchModel>();
            var visited = new HashSet<string>();

            foreach (var branch in branches)
            {
                if (!visited.Contains(branch.Id))
                {
                    VisitBranch(branch, branches, visited, sorted);
                }
            }

            return sorted;
        }

        /// <summary>
        /// 访问分支（深度优先遍历）
        /// </summary>
        private void VisitBranch(
            SFCBranchModel branch, 
            List<SFCBranchModel> allBranches, 
            HashSet<string> visited, 
            List<SFCBranchModel> sorted)
        {
            if (visited.Contains(branch.Id)) return;

            visited.Add(branch.Id);

            // 先访问父分支
            if (!string.IsNullOrEmpty(branch.ParentBranchId))
            {
                var parentBranch = allBranches.FirstOrDefault(b => b.Id == branch.ParentBranchId);
                if (parentBranch != null && !visited.Contains(parentBranch.Id))
                {
                    VisitBranch(parentBranch, allBranches, visited, sorted);
                }
            }

            sorted.Add(branch);
        }

        /// <summary>
        /// 获取分支的连接点信息
        /// </summary>
        private List<ConnectionPointInfo> GetBranchConnectionPoints(SFCBranchModel branch)
        {
            var connectionPoints = new List<ConnectionPointInfo>();

            // 根据分支类型计算连接点
            var positions = _sizeCalculator.CalculateConnectPointPositions(
                branch.CalculatedSize, 
                branch.BranchType);

            foreach (var kvp in positions)
            {
                connectionPoints.Add(new ConnectionPointInfo
                {
                    Index = kvp.Key,
                    Position = kvp.Value,
                    BranchId = branch.Id,
                    Type = DetermineConnectionPointType(kvp.Key, branch.BranchType)
                });
            }

            return connectionPoints;
        }

        /// <summary>
        /// 检测位置冲突
        /// </summary>
        private bool IsPositionConflict(Point pos1, Point pos2)
        {
            const double tolerance = 5.0; // 5像素容差
            var distance = Math.Sqrt(Math.Pow(pos1.X - pos2.X, 2) + Math.Pow(pos1.Y - pos2.Y, 2));
            return distance < tolerance;
        }

        /// <summary>
        /// 检测连接线交叉
        /// </summary>
        private void DetectConnectionLineCrossings(
            SFCBranchModel branch, 
            SFCModel context, 
            List<ConnectionPointConflict> conflicts)
        {
            // 实现连接线交叉检测逻辑
            // 这里简化实现，实际应该检测贝塞尔曲线的交叉
        }

        /// <summary>
        /// 生成冲突解决方案
        /// </summary>
        private async Task<ConflictSolution> GenerateConflictSolution(
            ConnectionPointConflict conflict, 
            SFCModel context)
        {
            switch (conflict.ConflictType)
            {
                case ConnectionPointConflictType.PositionOverlap:
                    return await GeneratePositionOverlapSolution(conflict, context);
                
                case ConnectionPointConflictType.ConnectionCrossing:
                    return await GenerateConnectionCrossingSolution(conflict, context);
                
                default:
                    return null;
            }
        }

        /// <summary>
        /// 生成位置重叠解决方案
        /// </summary>
        private async Task<ConflictSolution> GeneratePositionOverlapSolution(
            ConnectionPointConflict conflict, 
            SFCModel context)
        {
            // 实现位置重叠的解决方案
            // 例如：调整分支大小、重新排列连接点等
            await Task.CompletedTask;
            return new ConflictSolution
            {
                ConflictId = conflict.ConflictType.ToString(),
                SolutionType = ConflictSolutionType.AdjustBranchSize,
                Description = "调整分支大小以避免连接点重叠",
                Parameters = new Dictionary<string, object>()
            };
        }

        /// <summary>
        /// 生成连接线交叉解决方案
        /// </summary>
        private async Task<ConflictSolution> GenerateConnectionCrossingSolution(
            ConnectionPointConflict conflict, 
            SFCModel context)
        {
            // 实现连接线交叉的解决方案
            await Task.CompletedTask;
            return new ConflictSolution
            {
                ConflictId = conflict.ConflictType.ToString(),
                SolutionType = ConflictSolutionType.RerouteConnection,
                Description = "重新路由连接线以避免交叉",
                Parameters = new Dictionary<string, object>()
            };
        }

        /// <summary>
        /// 应用冲突解决方案
        /// </summary>
        private async Task<bool> ApplyConflictSolution(ConflictSolution solution, SFCModel context)
        {
            try
            {
                switch (solution.SolutionType)
                {
                    case ConflictSolutionType.AdjustBranchSize:
                        return await ApplyBranchSizeAdjustment(solution, context);
                    
                    case ConflictSolutionType.RerouteConnection:
                        return await ApplyConnectionRerouting(solution, context);
                    
                    default:
                        return false;
                }
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 应用分支大小调整
        /// </summary>
        private async Task<bool> ApplyBranchSizeAdjustment(ConflictSolution solution, SFCModel context)
        {
            // 实现分支大小调整逻辑
            await Task.CompletedTask;
            return true;
        }

        /// <summary>
        /// 应用连接线重新路由
        /// </summary>
        private async Task<bool> ApplyConnectionRerouting(ConflictSolution solution, SFCModel context)
        {
            // 实现连接线重新路由逻辑
            await Task.CompletedTask;
            return true;
        }

        /// <summary>
        /// 更新连接线端点
        /// </summary>
        private async Task UpdateConnectionEndpoints(
            SFCConnectionModel connection, 
            SFCModel context, 
            ConnectionPointSyncResult syncResult)
        {
            // 实现连接线端点更新逻辑
            await Task.CompletedTask;
        }

        /// <summary>
        /// 检查位置是否在分支边界内
        /// </summary>
        private bool IsPositionWithinBranchBounds(Point position, SFCBranchModel branch)
        {
            // 实现边界检查逻辑
            var size = branch.CalculatedSize;
            return position.X >= 0 && position.X <= size.Width &&
                   position.Y >= 0 && position.Y <= size.Height;
        }

        /// <summary>
        /// 确定连接点类型
        /// </summary>
        private ConnectionPointType DetermineConnectionPointType(int index, SFCBranchType branchType)
        {
            // 根据索引和分支类型确定连接点类型
            switch (branchType)
            {
                case SFCBranchType.Selection:
                    return index == 0 ? ConnectionPointType.Input : ConnectionPointType.Output;
                
                case SFCBranchType.Parallel:
                    return index == 0 ? ConnectionPointType.Input : ConnectionPointType.Output;
                
                default:
                    return ConnectionPointType.Input;
            }
        }

        #endregion
    }
}