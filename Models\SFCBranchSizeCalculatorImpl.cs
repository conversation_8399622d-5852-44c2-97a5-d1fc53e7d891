using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;

namespace PC_Control2.Demo.Models
{
    /// <summary>
    /// SFC分支尺寸计算器实现
    /// 负责计算分支的动态尺寸，考虑嵌套元素和子分支
    /// </summary>
    public class SFCBranchSizeCalculatorImpl : ISFCBranchSizeCalculator
    {
        #region 私有字段

        // 尺寸计算配置
        private readonly double _minimumBranchWidth = 120.0;
        private readonly double _minimumBranchHeight = 12.0;
        private readonly double _branchPadding = 10.0;
        private readonly double _elementSpacing = 15.0;
        private readonly double _connectionPointSpace = 8.0;
        private readonly double _headerHeight = 20.0;
        private readonly double _footerHeight = 10.0;

        // 分支类型特定的最小尺寸
        private readonly Dictionary<SFCBranchType, Size> _typeMinimumSizes = new()
        {
            { SFCBranchType.Selection, new Size(182, 50) },
            { SFCBranchType.Parallel, new Size(200, 50) },
            // 默认分支类型使用Selection的尺寸
        };

        #endregion

        #region ISFCBranchSizeCalculator 实现

        /// <summary>
        /// 异步计算分支尺寸
        /// </summary>
        public async Task<Size> CalculateBranchSizeAsync(
            SFCBranchModel branch, 
            List<NestedElementInfo> nestedElements, 
            Dictionary<string, Size> childSizes)
        {
            if (branch == null)
                throw new ArgumentNullException(nameof(branch));

            try
            {
                // 1. 获取基础尺寸
                var baseSize = GetBaseSizeForBranchType(branch.BranchType);

                // 2. 计算内容尺寸
                var contentSize = await CalculateContentSize(branch, nestedElements, childSizes);

                // 3. 计算最终尺寸
                var finalSize = CombineSizes(baseSize, contentSize, branch.BranchType);

                // 4. 应用约束和调整
                var constrainedSize = ApplyConstraints(finalSize, branch);

                return constrainedSize;
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"分支尺寸计算失败 (分支: {branch.Id}): {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 计算分支的最小尺寸
        /// </summary>
        public Size CalculateMinimumSize(SFCBranchModel branch, List<NestedElementInfo> nestedElements)
        {
            return CalculateMinimumRequiredSize(branch, nestedElements);
        }

        /// <summary>
        /// 计算分支的最优尺寸
        /// </summary>
        public Size CalculateOptimalSize(SFCBranchModel branch, List<NestedElementInfo> nestedElements)
        {
            var minSize = CalculateMinimumRequiredSize(branch, nestedElements);
            
            // 最优尺寸比最小尺寸大20%，提供更好的视觉效果
            return new Size(
                minSize.Width * 1.2,
                minSize.Height * 1.2
            );
        }

        /// <summary>
        /// 计算连接点位置
        /// </summary>
        public Dictionary<int, Point> CalculateConnectPointPositions(Size branchSize, SFCBranchType branchType)
        {
            var positions = new Dictionary<int, Point>();

            switch (branchType)
            {
                case SFCBranchType.Selection:
                    // 选择分支：4个连接点
                    positions[0] = new Point(0, 10); // LeftTop
                    positions[1] = new Point(0, branchSize.Height - 10); // LeftBottom
                    positions[2] = new Point(branchSize.Width, 10); // RightTop
                    positions[3] = new Point(branchSize.Width, branchSize.Height - 10); // RightBottom
                    break;

                case SFCBranchType.Parallel:
                    // 并行分支：3个连接点
                    positions[0] = new Point(0, 10); // LeftTop
                    positions[1] = new Point(0, branchSize.Height / 2); // LeftParallel
                    positions[2] = new Point(branchSize.Width, branchSize.Height / 2); // RightParallel
                    break;

                default:
                    // 默认连接点
                    positions[0] = new Point(branchSize.Width / 2, 0); // Top
                    positions[1] = new Point(branchSize.Width / 2, branchSize.Height); // Bottom
                    break;
            }

            return positions;
        }

        /// <summary>
        /// 计算水平线扩展长度
        /// </summary>
        public double CalculateHorizontalLineExtension(int childBranchCount, int nestingLevel)
        {
            // 基础扩展长度
            var baseExtension = 20.0;
            
            // 根据子分支数量调整
            var countFactor = Math.Max(1, childBranchCount) * 10.0;
            
            // 根据嵌套层级调整
            var levelFactor = nestingLevel * 5.0;
            
            return baseExtension + countFactor + levelFactor;
        }

        /// <summary>
        /// 计算子分支占用的空间
        /// </summary>
        public Size CalculateChildrenSpace(List<BranchLayoutResult> childBranches, SFCBranchType branchType)
        {
            if (childBranches == null || childBranches.Count == 0)
                return new Size(0, 0);

            var totalWidth = 0.0;
            var totalHeight = 0.0;
            var maxWidth = 0.0;
            var maxHeight = 0.0;

            foreach (var child in childBranches)
            {
                totalWidth += child.CalculatedSize.Width;
                totalHeight += child.CalculatedSize.Height;
                maxWidth = Math.Max(maxWidth, child.CalculatedSize.Width);
                maxHeight = Math.Max(maxHeight, child.CalculatedSize.Height);
            }

            // 根据分支类型选择布局方式
            return branchType switch
            {
                SFCBranchType.Parallel => new Size(
                    totalWidth + (childBranches.Count - 1) * _elementSpacing,
                    maxHeight),
                
                _ => new Size(
                    maxWidth,
                    totalHeight + (childBranches.Count - 1) * _elementSpacing)
            };
        }

        /// <summary>
        /// 计算最小所需尺寸
        /// </summary>
        public Size CalculateMinimumRequiredSize(SFCBranchModel branch, List<NestedElementInfo> nestedElements)
        {
            if (branch == null)
                throw new ArgumentNullException(nameof(branch));

            // 获取分支类型的最小尺寸
            var typeMinSize = GetBaseSizeForBranchType(branch.BranchType);

            // 计算嵌套元素所需的最小空间
            var nestedMinSize = CalculateNestedElementsMinimumSize(nestedElements, branch.BranchType);

            // 返回较大的尺寸
            return new Size(
                Math.Max(typeMinSize.Width, nestedMinSize.Width),
                Math.Max(typeMinSize.Height, nestedMinSize.Height)
            );
        }

        /// <summary>
        /// 验证尺寸是否有效
        /// </summary>
        public bool ValidateSize(Size size, SFCBranchModel branch)
        {
            if (branch == null) return false;

            var minSize = GetBaseSizeForBranchType(branch.BranchType);
            
            return size.Width >= minSize.Width && 
                   size.Height >= minSize.Height &&
                   size.Width <= 10000 && // 最大宽度限制
                   size.Height <= 10000;  // 最大高度限制
        }

        /// <summary>
        /// 优化尺寸以提高性能
        /// </summary>
        public Size OptimizeSize(Size calculatedSize, SFCBranchModel branch, PerformanceHint hint)
        {
            switch (hint)
            {
                case PerformanceHint.MinimizeMemory:
                    // 最小化内存使用，使用较小的尺寸
                    return ReduceSizeForMemory(calculatedSize, branch);

                case PerformanceHint.MaximizeSpeed:
                    // 最大化渲染速度，使用标准尺寸
                    return StandardizeSize(calculatedSize, branch);

                case PerformanceHint.BalanceQuality:
                    // 平衡质量和性能
                    return BalanceSize(calculatedSize, branch);

                default:
                    return calculatedSize;
            }
        }

        #endregion

        #region 核心计算方法

        /// <summary>
        /// 计算内容尺寸
        /// </summary>
        private async Task<Size> CalculateContentSize(
            SFCBranchModel branch, 
            List<NestedElementInfo> nestedElements, 
            Dictionary<string, Size> childSizes)
        {
            var contentWidth = 0.0;
            var contentHeight = 0.0;

            // 根据分支类型计算内容布局
            switch (branch.BranchType)
            {
                case SFCBranchType.Selection:
                    (contentWidth, contentHeight) = await CalculateSelectionBranchContentSize(nestedElements, childSizes);
                    break;

                case SFCBranchType.Parallel:
                    (contentWidth, contentHeight) = await CalculateParallelBranchContentSize(nestedElements, childSizes);
                    break;

                default:
                    (contentWidth, contentHeight) = await CalculateSimpleBranchContentSize(nestedElements, childSizes);
                    break;
            }

            return new Size(contentWidth, contentHeight);
        }

        /// <summary>
        /// 计算选择分支内容尺寸
        /// </summary>
        private async Task<(double width, double height)> CalculateSelectionBranchContentSize(
            List<NestedElementInfo> nestedElements, 
            Dictionary<string, Size> childSizes)
        {
            var maxWidth = 0.0;
            var totalHeight = _headerHeight + _footerHeight;

            // 垂直布局：计算最大宽度和总高度
            foreach (var element in nestedElements.OrderBy(e => e.IndexInLevel))
            {
                var elementSize = GetElementSize(element, childSizes);
                maxWidth = Math.Max(maxWidth, elementSize.Width);
                totalHeight += elementSize.Height + _elementSpacing;
            }

            // 添加内边距
            maxWidth += 2 * _branchPadding;
            totalHeight += 2 * _branchPadding;

            return (maxWidth, totalHeight);
        }

        /// <summary>
        /// 计算并行分支内容尺寸
        /// </summary>
        private async Task<(double width, double height)> CalculateParallelBranchContentSize(
            List<NestedElementInfo> nestedElements, 
            Dictionary<string, Size> childSizes)
        {
            var totalWidth = 0.0;
            var maxHeight = _headerHeight + _footerHeight;

            // 水平布局：计算总宽度和最大高度
            foreach (var element in nestedElements.OrderBy(e => e.IndexInLevel))
            {
                var elementSize = GetElementSize(element, childSizes);
                totalWidth += elementSize.Width + _elementSpacing;
                maxHeight = Math.Max(maxHeight, elementSize.Height);
            }

            // 添加内边距
            totalWidth += 2 * _branchPadding;
            maxHeight += 2 * _branchPadding;

            return (totalWidth, maxHeight);
        }

        /// <summary>
        /// 计算简单分支内容尺寸
        /// </summary>
        private async Task<(double width, double height)> CalculateSimpleBranchContentSize(
            List<NestedElementInfo> nestedElements, 
            Dictionary<string, Size> childSizes)
        {
            var maxWidth = 0.0;
            var totalHeight = 0.0;

            // 简单垂直布局
            foreach (var element in nestedElements)
            {
                var elementSize = GetElementSize(element, childSizes);
                maxWidth = Math.Max(maxWidth, elementSize.Width);
                totalHeight += elementSize.Height + _elementSpacing;
            }

            // 添加内边距
            maxWidth += 2 * _branchPadding;
            totalHeight += 2 * _branchPadding;

            return (maxWidth, totalHeight);
        }

        /// <summary>
        /// 获取元素尺寸
        /// </summary>
        private Size GetElementSize(NestedElementInfo element, Dictionary<string, Size> childSizes)
        {
            // 如果是子分支，从childSizes中获取
            if (element.ElementType == SFCElementType.Branch && childSizes.ContainsKey(element.ElementId))
            {
                return childSizes[element.ElementId];
            }

            // 根据元素类型返回默认尺寸
            return GetDefaultElementSize(element.ElementType);
        }

        /// <summary>
        /// 获取默认元素尺寸
        /// </summary>
        private Size GetDefaultElementSize(SFCElementType elementType)
        {
            return elementType switch
            {
                SFCElementType.Step => new Size(80, 40),
                SFCElementType.Transition => new Size(60, 20),
                SFCElementType.Branch => new Size(120, 50),
                SFCElementType.Action => new Size(100, 30),
                _ => new Size(60, 20)
            };
        }

        #endregion

        #region 尺寸组合和约束

        /// <summary>
        /// 组合基础尺寸和内容尺寸
        /// </summary>
        private Size CombineSizes(Size baseSize, Size contentSize, SFCBranchType branchType)
        {
            var combinedWidth = Math.Max(baseSize.Width, contentSize.Width);
            var combinedHeight = Math.Max(baseSize.Height, contentSize.Height);

            // 根据分支类型进行特殊处理
            switch (branchType)
            {
                case SFCBranchType.Selection:
                    // 选择分支需要额外的头部和尾部空间
                    combinedHeight += _headerHeight + _footerHeight;
                    break;

                case SFCBranchType.Parallel:
                    // 并行分支需要额外的连接线空间
                    combinedWidth += 2 * _connectionPointSpace;
                    combinedHeight += _headerHeight + _footerHeight;
                    break;
            }

            return new Size(combinedWidth, combinedHeight);
        }

        /// <summary>
        /// 应用尺寸约束
        /// </summary>
        private Size ApplyConstraints(Size size, SFCBranchModel branch)
        {
            var minSize = GetBaseSizeForBranchType(branch.BranchType);
            
            var constrainedWidth = Math.Max(size.Width, minSize.Width);
            var constrainedHeight = Math.Max(size.Height, minSize.Height);

            // 应用最大尺寸限制
            constrainedWidth = Math.Min(constrainedWidth, 2000); // 最大宽度
            constrainedHeight = Math.Min(constrainedHeight, 2000); // 最大高度

            // 确保尺寸为整数（避免渲染问题）
            constrainedWidth = Math.Ceiling(constrainedWidth);
            constrainedHeight = Math.Ceiling(constrainedHeight);

            return new Size(constrainedWidth, constrainedHeight);
        }

        /// <summary>
        /// 获取分支类型的基础尺寸
        /// </summary>
        private Size GetBaseSizeForBranchType(SFCBranchType branchType)
        {
            return _typeMinimumSizes.ContainsKey(branchType) 
                ? _typeMinimumSizes[branchType] 
                : new Size(_minimumBranchWidth, _minimumBranchHeight);
        }

        /// <summary>
        /// 计算嵌套元素的最小尺寸
        /// </summary>
        private Size CalculateNestedElementsMinimumSize(List<NestedElementInfo> nestedElements, SFCBranchType branchType)
        {
            if (nestedElements == null || nestedElements.Count == 0)
                return new Size(0, 0);

            var totalWidth = 0.0;
            var totalHeight = 0.0;
            var maxWidth = 0.0;
            var maxHeight = 0.0;

            foreach (var element in nestedElements)
            {
                var elementSize = GetDefaultElementSize(element.ElementType);
                
                totalWidth += elementSize.Width;
                totalHeight += elementSize.Height;
                maxWidth = Math.Max(maxWidth, elementSize.Width);
                maxHeight = Math.Max(maxHeight, elementSize.Height);
            }

            // 根据分支类型选择布局方式
            return branchType switch
            {
                SFCBranchType.Parallel => new Size(
                    totalWidth + (nestedElements.Count - 1) * _elementSpacing + 2 * _branchPadding,
                    maxHeight + 2 * _branchPadding + _headerHeight + _footerHeight),
                
                _ => new Size(
                    maxWidth + 2 * _branchPadding,
                    totalHeight + (nestedElements.Count - 1) * _elementSpacing + 2 * _branchPadding)
            };
        }

        #endregion

        #region 性能优化方法

        /// <summary>
        /// 为内存优化减少尺寸
        /// </summary>
        private Size ReduceSizeForMemory(Size size, SFCBranchModel branch)
        {
            var minSize = GetBaseSizeForBranchType(branch.BranchType);
            
            // 使用最小可能的尺寸
            return new Size(
                Math.Max(size.Width * 0.9, minSize.Width),
                Math.Max(size.Height * 0.9, minSize.Height)
            );
        }

        /// <summary>
        /// 标准化尺寸以提高渲染速度
        /// </summary>
        private Size StandardizeSize(Size size, SFCBranchModel branch)
        {
            // 将尺寸标准化为8的倍数（有利于GPU渲染）
            var standardWidth = Math.Ceiling(size.Width / 8) * 8;
            var standardHeight = Math.Ceiling(size.Height / 8) * 8;
            
            return new Size(standardWidth, standardHeight);
        }

        /// <summary>
        /// 平衡尺寸质量和性能
        /// </summary>
        private Size BalanceSize(Size size, SFCBranchModel branch)
        {
            // 在质量和性能之间找到平衡
            var balancedWidth = Math.Ceiling(size.Width / 4) * 4;
            var balancedHeight = Math.Ceiling(size.Height / 4) * 4;
            
            return new Size(balancedWidth, balancedHeight);
        }

        #endregion
    }

    /// <summary>
    /// 性能提示枚举
    /// </summary>
    public enum PerformanceHint
    {
        /// <summary>
        /// 最小化内存使用
        /// </summary>
        MinimizeMemory,

        /// <summary>
        /// 最大化渲染速度
        /// </summary>
        MaximizeSpeed,

        /// <summary>
        /// 平衡质量和性能
        /// </summary>
        BalanceQuality
    }
}