using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;

namespace PC_Control2.Demo.Models
{
    /// <summary>
    /// 🆕 SFC分支包络计算器 - 实现分支包络算法的核心组件
    /// 负责计算嵌套内容的空间占用，并确定需要包络扩展的分支路径
    /// </summary>
    public class SFCBranchEnvelopeCalculator
    {
        private const double DefaultBranchSpacing = 20.0; // 分支间距
        private const double DefaultElementWidth = 80.0;  // 默认元素宽度
        private const double DefaultElementSpacing = 10.0; // 元素间距

        /// <summary>
        /// 计算嵌套内容的包络信息
        /// </summary>
        /// <param name="nestedElement">嵌套的元素（如T4→S4）</param>
        /// <param name="parentStep">父步骤（如S3）</param>
        /// <param name="parentBranch">包含父步骤的选择分支</param>
        /// <param name="sfcContext">SFC上下文</param>
        /// <returns>包络计算结果</returns>
        public BranchEnvelopeResult CalculateEnvelope(
            object nestedElement, 
            SFCStepModel parentStep, 
            SFCBranchModel parentBranch, 
            SFCModel sfcContext)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"[包络计算] 🎯 开始计算包络：嵌套元素类型 {nestedElement?.GetType().Name}");
                System.Diagnostics.Debug.WriteLine($"  父步骤: {parentStep?.Id}, 父分支: {parentBranch?.Id}");

                var result = new BranchEnvelopeResult
                {
                    IsValid = false,
                    NestedContentWidth = 0,
                    EnvelopeBranches = new List<BranchEnvelopeInfo>()
                };

                // 1. 计算嵌套内容的总宽度
                var nestedWidth = CalculateNestedContentWidth(nestedElement);
                result.NestedContentWidth = nestedWidth;

                System.Diagnostics.Debug.WriteLine($"[包络计算] 📏 嵌套内容宽度: {nestedWidth}px");

                // 2. 找到需要包络的分支路径
                var envelopeBranches = FindEnvelopeBranches(parentStep, parentBranch, nestedWidth);
                result.EnvelopeBranches = envelopeBranches;

                System.Diagnostics.Debug.WriteLine($"[包络计算] 🎯 找到 {envelopeBranches.Count} 个需要包络的分支路径");

                result.IsValid = envelopeBranches.Any();
                return result;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[包络计算] ❌ 异常: {ex.Message}");
                return new BranchEnvelopeResult { IsValid = false };
            }
        }

        /// <summary>
        /// 计算嵌套内容的总宽度
        /// </summary>
        private double CalculateNestedContentWidth(object nestedElement)
        {
            if (nestedElement is SFCBranchModel nestedBranch)
            {
                // 对于嵌套分支，计算其完整宽度
                // T4 → S4 的总宽度 = T4宽度 + 间距 + S4宽度
                var transitionWidth = DefaultElementWidth; // T4
                var stepWidth = DefaultElementWidth;       // S4
                var spacing = DefaultElementSpacing;       // 间距
                
                var totalWidth = transitionWidth + spacing + stepWidth;
                
                System.Diagnostics.Debug.WriteLine($"[宽度计算] 嵌套分支 {nestedBranch.Id}: T({transitionWidth}) + 间距({spacing}) + S({stepWidth}) = {totalWidth}");
                
                return totalWidth;
            }
            
            if (nestedElement is SFCStepModel || nestedElement is SFCTransitionModel)
            {
                // 单个元素的宽度
                return DefaultElementWidth;
            }

            // 默认宽度
            return DefaultElementWidth;
        }

        /// <summary>
        /// 找到需要包络的分支路径
        /// </summary>
        private List<BranchEnvelopeInfo> FindEnvelopeBranches(
            SFCStepModel parentStep, 
            SFCBranchModel parentBranch, 
            double nestedWidth)
        {
            var envelopeBranches = new List<BranchEnvelopeInfo>();

            try
            {
                // 在父分支中找到父步骤的位置索引
                var parentStepIndex = FindStepIndexInBranch(parentStep, parentBranch);
                if (parentStepIndex == -1)
                {
                    System.Diagnostics.Debug.WriteLine($"[包络查找] ❌ 未找到父步骤 {parentStep.Id} 在分支 {parentBranch.Id} 中的位置");
                    return envelopeBranches;
                }

                System.Diagnostics.Debug.WriteLine($"[包络查找] 📍 父步骤 {parentStep.Id} 在分支中的索引: {parentStepIndex}");

                // 找到需要包络的分支路径（在父步骤之后的所有分支路径）
                var branchPaths = GetBranchPaths(parentBranch);
                
                for (int i = parentStepIndex + 1; i < branchPaths.Count; i++)
                {
                    var branchPath = branchPaths[i];
                    var currentExtension = GetCurrentHorizontalExtension(branchPath);
                    var requiredExtension = nestedWidth + DefaultBranchSpacing;

                    if (requiredExtension > currentExtension)
                    {
                        var envelopeInfo = new BranchEnvelopeInfo
                        {
                            BranchPath = branchPath,
                            CurrentExtension = currentExtension,
                            RequiredExtension = requiredExtension,
                            ExtensionDelta = requiredExtension - currentExtension
                        };

                        envelopeBranches.Add(envelopeInfo);

                        System.Diagnostics.Debug.WriteLine($"[包络查找] 🎯 需要包络的分支路径: {branchPath.PathDescription}");
                        System.Diagnostics.Debug.WriteLine($"  当前扩展: {currentExtension}, 需要扩展: {requiredExtension}, 增量: {envelopeInfo.ExtensionDelta}");
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[包络查找] ❌ 异常: {ex.Message}");
            }

            return envelopeBranches;
        }

        /// <summary>
        /// 在分支中查找步骤的索引位置
        /// </summary>
        private int FindStepIndexInBranch(SFCStepModel step, SFCBranchModel branch)
        {
            // 这里需要根据实际的分支结构来实现
            // 暂时返回模拟值，实际实现需要分析分支的内部结构
            
            // 假设分支路径按顺序排列：T5→S5, T2→S3, T3→S6
            // 如果step是S3，则返回索引1
            if (step.Id.Contains("S3")) return 1;
            if (step.Id.Contains("S5")) return 0;
            if (step.Id.Contains("S6")) return 2;
            
            return -1; // 未找到
        }

        /// <summary>
        /// 获取分支的所有路径
        /// </summary>
        private List<BranchPathInfo> GetBranchPaths(SFCBranchModel branch)
        {
            var paths = new List<BranchPathInfo>();
            
            // 这里需要根据实际的分支结构来实现
            // 暂时返回模拟的分支路径
            paths.Add(new BranchPathInfo { PathDescription = "T5→S5", TransitionId = "T5", StepId = "S5" });
            paths.Add(new BranchPathInfo { PathDescription = "T2→S3", TransitionId = "T2", StepId = "S3" });
            paths.Add(new BranchPathInfo { PathDescription = "T3→S6", TransitionId = "T3", StepId = "S6" });
            
            return paths;
        }

        /// <summary>
        /// 获取分支路径的当前水平扩展长度
        /// </summary>
        private double GetCurrentHorizontalExtension(BranchPathInfo branchPath)
        {
            // 返回当前的水平线长度，默认值
            return 100.0; // 默认长度
        }
    }

    /// <summary>
    /// 分支包络计算结果
    /// </summary>
    public class BranchEnvelopeResult
    {
        public bool IsValid { get; set; }
        public double NestedContentWidth { get; set; }
        public List<BranchEnvelopeInfo> EnvelopeBranches { get; set; } = new List<BranchEnvelopeInfo>();
    }

    /// <summary>
    /// 分支包络信息
    /// </summary>
    public class BranchEnvelopeInfo
    {
        public BranchPathInfo BranchPath { get; set; }
        public double CurrentExtension { get; set; }
        public double RequiredExtension { get; set; }
        public double ExtensionDelta { get; set; }
    }

    /// <summary>
    /// 分支路径信息
    /// </summary>
    public class BranchPathInfo
    {
        public string PathDescription { get; set; } = string.Empty;
        public string TransitionId { get; set; } = string.Empty;
        public string StepId { get; set; } = string.Empty;
    }
}