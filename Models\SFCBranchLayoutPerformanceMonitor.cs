using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading.Tasks;

namespace PC_Control2.Demo.Models
{
    /// <summary>
    /// SFC分支布局性能监控器
    /// 监控布局计算的性能指标，提供性能分析和优化建议
    /// </summary>
    public class SFCBranchLayoutPerformanceMonitor : ISFCBranchLayoutPerformanceMonitor
    {
        #region 私有字段

        private readonly Dictionary<string, PerformanceMetrics> _metricsHistory = new();
        private readonly Dictionary<string, Stopwatch> _activeTimers = new();
        private readonly object _lockObject = new();
        private readonly int _maxHistoryCount = 1000;
        private readonly TimeSpan _performanceThreshold = TimeSpan.FromMilliseconds(100);

        #endregion

        #region 性能监控核心方法

        /// <summary>
        /// 开始监控布局计算性能
        /// </summary>
        public string StartLayoutCalculationMonitoring(string branchId, string operationType)
        {
            lock (_lockObject)
            {
                var monitoringId = Guid.NewGuid().ToString();
                var stopwatch = Stopwatch.StartNew();
                
                _activeTimers[monitoringId] = stopwatch;
                
                // 记录开始时间和操作信息
                var startMetrics = new PerformanceMetrics
                {
                    BranchId = branchId,
                    OperationType = operationType,
                    StartTime = DateTime.Now,
                    MonitoringId = monitoringId,
                    Status = PerformanceStatus.InProgress
                };
                
                _metricsHistory[monitoringId] = startMetrics;
                
                return monitoringId;
            }
        }

        /// <summary>
        /// 结束监控并记录性能指标
        /// </summary>
        public PerformanceMetrics EndLayoutCalculationMonitoring(
            string monitoringId, 
            LayoutCalculationResult result)
        {
            lock (_lockObject)
            {
                if (!_activeTimers.TryGetValue(monitoringId, out var stopwatch))
                {
                    throw new InvalidOperationException($"未找到监控ID: {monitoringId}");
                }

                stopwatch.Stop();
                _activeTimers.Remove(monitoringId);

                if (!_metricsHistory.TryGetValue(monitoringId, out var metrics))
                {
                    throw new InvalidOperationException($"未找到性能指标记录: {monitoringId}");
                }

                // 更新性能指标
                metrics.EndTime = DateTime.Now;
                metrics.Duration = stopwatch.Elapsed;
                metrics.MemoryUsage = GC.GetTotalMemory(false);
                metrics.Status = result.IsSuccessful ? PerformanceStatus.Completed : PerformanceStatus.Failed;
                metrics.ElementCount = result.ProcessedElementCount;
                metrics.CacheHitCount = result.CacheHitCount;
                metrics.CacheMissCount = result.CacheMissCount;
                metrics.ErrorMessage = result.ErrorMessage;

                // 计算性能评级
                metrics.PerformanceRating = CalculatePerformanceRating(metrics);

                // 清理历史记录
                CleanupOldMetrics();

                return metrics;
            }
        }

        /// <summary>
        /// 记录布局操作的详细信息
        /// </summary>
        public void RecordLayoutOperation(
            string monitoringId, 
            string operationName, 
            TimeSpan duration, 
            Dictionary<string, object>? additionalData = null)
        {
            lock (_lockObject)
            {
                if (!_metricsHistory.TryGetValue(monitoringId, out var metrics))
                {
                    return;
                }

                var operation = new LayoutOperationRecord
                {
                    OperationName = operationName,
                    Duration = duration,
                    Timestamp = DateTime.Now,
                    AdditionalData = additionalData ?? new Dictionary<string, object>()
                };

                metrics.Operations.Add(operation);
            }
        }

        #endregion

        #region 性能分析方法

        /// <summary>
        /// 获取性能统计信息
        /// </summary>
        public PerformanceStatistics GetPerformanceStatistics(TimeSpan? timeRange = null)
        {
            lock (_lockObject)
            {
                var cutoffTime = timeRange.HasValue ? DateTime.Now - timeRange.Value : DateTime.MinValue;
                var relevantMetrics = _metricsHistory.Values
                    .Where(m => m.StartTime >= cutoffTime && m.Status == PerformanceStatus.Completed)
                    .ToList();

                if (!relevantMetrics.Any())
                {
                    return new PerformanceStatistics();
                }

                var durations = relevantMetrics.Select(m => m.Duration).ToList();
                var elementCounts = relevantMetrics.Select(m => m.ElementCount).ToList();

                return new PerformanceStatistics
                {
                    TotalOperations = relevantMetrics.Count,
                    AverageDuration = TimeSpan.FromTicks((long)durations.Average(d => d.Ticks)),
                    MinDuration = durations.Min(),
                    MaxDuration = durations.Max(),
                    AverageElementCount = elementCounts.Any() ? (int)elementCounts.Average() : 0,
                    SlowOperationsCount = relevantMetrics.Count(m => m.Duration > _performanceThreshold),
                    CacheHitRate = CalculateCacheHitRate(relevantMetrics),
                    PerformanceRatingDistribution = CalculateRatingDistribution(relevantMetrics),
                    TimeRange = timeRange ?? TimeSpan.FromDays(1)
                };
            }
        }

        /// <summary>
        /// 获取性能瓶颈分析
        /// </summary>
        public List<PerformanceBottleneck> AnalyzePerformanceBottlenecks(int topCount = 10)
        {
            lock (_lockObject)
            {
                var bottlenecks = new List<PerformanceBottleneck>();
                var recentMetrics = _metricsHistory.Values
                    .Where(m => m.Status == PerformanceStatus.Completed)
                    .Where(m => m.StartTime >= DateTime.Now.AddHours(-24))
                    .ToList();

                // 分析慢操作
                var slowOperations = recentMetrics
                    .Where(m => m.Duration > _performanceThreshold)
                    .GroupBy(m => m.OperationType)
                    .Select(g => new PerformanceBottleneck
                    {
                        BottleneckType = BottleneckType.SlowOperation,
                        Description = $"操作类型 '{g.Key}' 执行缓慢",
                        AffectedOperationType = g.Key,
                        Frequency = g.Count(),
                        AverageDuration = TimeSpan.FromTicks((long)g.Average(m => m.Duration.Ticks)),
                        Severity = CalculateBottleneckSeverity(g.Average(m => m.Duration.TotalMilliseconds)),
                        Recommendations = GenerateSlowOperationRecommendations(g.Key, g.Average(m => m.Duration.TotalMilliseconds))
                    })
                    .OrderByDescending(b => b.Severity)
                    .Take(topCount / 2);

                bottlenecks.AddRange(slowOperations);

                // 分析缓存命中率低的操作
                var lowCacheHitOperations = recentMetrics
                    .Where(m => m.CacheHitCount + m.CacheMissCount > 0)
                    .GroupBy(m => m.OperationType)
                    .Where(g => CalculateGroupCacheHitRate(g) < 0.5)
                    .Select(g => new PerformanceBottleneck
                    {
                        BottleneckType = BottleneckType.LowCacheHitRate,
                        Description = $"操作类型 '{g.Key}' 缓存命中率低",
                        AffectedOperationType = g.Key,
                        Frequency = g.Count(),
                        CacheHitRate = CalculateGroupCacheHitRate(g),
                        Severity = BottleneckSeverity.Medium,
                        Recommendations = GenerateCacheRecommendations(g.Key, CalculateGroupCacheHitRate(g))
                    })
                    .Take(topCount / 2);

                bottlenecks.AddRange(lowCacheHitOperations);

                return bottlenecks.OrderByDescending(b => b.Severity).Take(topCount).ToList();
            }
        }

        /// <summary>
        /// 获取性能优化建议
        /// </summary>
        public List<PerformanceRecommendation> GetPerformanceRecommendations()
        {
            var recommendations = new List<PerformanceRecommendation>();
            var statistics = GetPerformanceStatistics(TimeSpan.FromHours(24));

            // 基于平均执行时间的建议
            if (statistics.AverageDuration > _performanceThreshold)
            {
                recommendations.Add(new PerformanceRecommendation
                {
                    Type = RecommendationType.Performance,
                    Priority = RecommendationPriority.High,
                    Title = "整体性能优化",
                    Description = $"平均执行时间 ({statistics.AverageDuration.TotalMilliseconds:F2}ms) 超过阈值 ({_performanceThreshold.TotalMilliseconds}ms)",
                    Suggestions = new List<string>
                    {
                        "考虑优化布局算法的时间复杂度",
                        "增加更多的缓存策略",
                        "使用异步并行计算",
                        "减少不必要的重复计算"
                    }
                });
            }

            // 基于缓存命中率的建议
            if (statistics.CacheHitRate < 0.7)
            {
                recommendations.Add(new PerformanceRecommendation
                {
                    Type = RecommendationType.Cache,
                    Priority = RecommendationPriority.Medium,
                    Title = "缓存优化",
                    Description = $"缓存命中率 ({statistics.CacheHitRate:P2}) 较低",
                    Suggestions = new List<string>
                    {
                        "增加缓存容量",
                        "优化缓存键的设计",
                        "实现更智能的缓存失效策略",
                        "考虑使用分层缓存"
                    }
                });
            }

            // 基于慢操作数量的建议
            if (statistics.SlowOperationsCount > statistics.TotalOperations * 0.1)
            {
                recommendations.Add(new PerformanceRecommendation
                {
                    Type = RecommendationType.Algorithm,
                    Priority = RecommendationPriority.High,
                    Title = "算法优化",
                    Description = $"慢操作比例 ({(double)statistics.SlowOperationsCount / statistics.TotalOperations:P2}) 过高",
                    Suggestions = new List<string>
                    {
                        "分析慢操作的具体原因",
                        "优化数据结构和算法",
                        "考虑使用增量计算",
                        "实现操作的批量处理"
                    }
                });
            }

            return recommendations.OrderByDescending(r => r.Priority).ToList();
        }

        #endregion

        #region 辅助方法

        /// <summary>
        /// 计算性能评级
        /// </summary>
        private PerformanceRating CalculatePerformanceRating(PerformanceMetrics metrics)
        {
            var durationMs = metrics.Duration.TotalMilliseconds;
            
            if (durationMs <= 10) return PerformanceRating.Excellent;
            if (durationMs <= 50) return PerformanceRating.Good;
            if (durationMs <= 100) return PerformanceRating.Fair;
            if (durationMs <= 500) return PerformanceRating.Poor;
            return PerformanceRating.Critical;
        }

        /// <summary>
        /// 计算缓存命中率
        /// </summary>
        private double CalculateCacheHitRate(List<PerformanceMetrics> metrics)
        {
            var totalHits = metrics.Sum(m => m.CacheHitCount);
            var totalRequests = metrics.Sum(m => m.CacheHitCount + m.CacheMissCount);
            
            return totalRequests > 0 ? (double)totalHits / totalRequests : 0.0;
        }

        /// <summary>
        /// 计算组缓存命中率
        /// </summary>
        private double CalculateGroupCacheHitRate(IGrouping<string, PerformanceMetrics> group)
        {
            var totalHits = group.Sum(m => m.CacheHitCount);
            var totalRequests = group.Sum(m => m.CacheHitCount + m.CacheMissCount);
            
            return totalRequests > 0 ? (double)totalHits / totalRequests : 0.0;
        }

        /// <summary>
        /// 计算评级分布
        /// </summary>
        private Dictionary<PerformanceRating, int> CalculateRatingDistribution(List<PerformanceMetrics> metrics)
        {
            return metrics
                .GroupBy(m => m.PerformanceRating)
                .ToDictionary(g => g.Key, g => g.Count());
        }

        /// <summary>
        /// 计算瓶颈严重程度
        /// </summary>
        private BottleneckSeverity CalculateBottleneckSeverity(double averageDurationMs)
        {
            if (averageDurationMs > 1000) return BottleneckSeverity.Critical;
            if (averageDurationMs > 500) return BottleneckSeverity.High;
            if (averageDurationMs > 200) return BottleneckSeverity.Medium;
            return BottleneckSeverity.Low;
        }

        /// <summary>
        /// 生成慢操作建议
        /// </summary>
        private List<string> GenerateSlowOperationRecommendations(string operationType, double averageDurationMs)
        {
            var recommendations = new List<string>();
            
            switch (operationType.ToLower())
            {
                case "nestingdetection":
                    recommendations.Add("优化空间查询算法，使用空间索引");
                    recommendations.Add("减少重复的碰撞检测计算");
                    break;
                case "sizecalculation":
                    recommendations.Add("缓存中间计算结果");
                    recommendations.Add("使用增量尺寸计算");
                    break;
                case "layoutengine":
                    recommendations.Add("并行化布局计算");
                    recommendations.Add("优化布局算法的时间复杂度");
                    break;
                default:
                    recommendations.Add("分析具体的性能瓶颈");
                    recommendations.Add("考虑异步处理");
                    break;
            }
            
            if (averageDurationMs > 500)
            {
                recommendations.Add("考虑将操作拆分为更小的步骤");
            }
            
            return recommendations;
        }

        /// <summary>
        /// 生成缓存建议
        /// </summary>
        private List<string> GenerateCacheRecommendations(string operationType, double cacheHitRate)
        {
            var recommendations = new List<string>
            {
                "增加缓存容量",
                "优化缓存键的设计"
            };
            
            if (cacheHitRate < 0.3)
            {
                recommendations.Add("检查缓存失效策略是否过于激进");
                recommendations.Add("考虑预加载常用数据");
            }
            
            return recommendations;
        }

        /// <summary>
        /// 清理旧的性能指标记录
        /// </summary>
        private void CleanupOldMetrics()
        {
            if (_metricsHistory.Count <= _maxHistoryCount) return;
            
            var oldestEntries = _metricsHistory.Values
                .OrderBy(m => m.StartTime)
                .Take(_metricsHistory.Count - _maxHistoryCount)
                .Select(m => m.MonitoringId)
                .ToList();
            
            foreach (var id in oldestEntries)
            {
                _metricsHistory.Remove(id);
            }
        }

        #endregion

        #region 导出和报告方法

        /// <summary>
        /// 导出性能报告
        /// </summary>
        public async Task<PerformanceReport> GeneratePerformanceReportAsync(TimeSpan timeRange)
        {
            return await Task.Run(() =>
            {
                lock (_lockObject)
                {
                    var statistics = GetPerformanceStatistics(timeRange);
                    var bottlenecks = AnalyzePerformanceBottlenecks();
                    var recommendations = GetPerformanceRecommendations();
                    
                    return new PerformanceReport
                    {
                        GeneratedAt = DateTime.Now,
                        TimeRange = timeRange,
                        Statistics = statistics,
                        Bottlenecks = bottlenecks,
                        Recommendations = recommendations,
                        Summary = GenerateReportSummary(statistics, bottlenecks, recommendations)
                    };
                }
            });
        }

        /// <summary>
        /// 生成报告摘要
        /// </summary>
        private string GenerateReportSummary(
            PerformanceStatistics statistics, 
            List<PerformanceBottleneck> bottlenecks, 
            List<PerformanceRecommendation> recommendations)
        {
            var summary = $"性能报告摘要:\n";
            summary += $"- 总操作数: {statistics.TotalOperations}\n";
            summary += $"- 平均执行时间: {statistics.AverageDuration.TotalMilliseconds:F2}ms\n";
            summary += $"- 缓存命中率: {statistics.CacheHitRate:P2}\n";
            summary += $"- 慢操作数: {statistics.SlowOperationsCount}\n";
            summary += $"- 发现瓶颈: {bottlenecks.Count}个\n";
            summary += $"- 优化建议: {recommendations.Count}条\n";
            
            if (bottlenecks.Any(b => b.Severity == BottleneckSeverity.Critical))
            {
                summary += "⚠️ 发现严重性能问题，需要立即处理\n";
            }
            else if (statistics.AverageDuration > _performanceThreshold)
            {
                summary += "⚠️ 整体性能需要优化\n";
            }
            else
            {
                summary += "✅ 整体性能表现良好\n";
            }
            
            return summary;
        }

        #endregion
    }
}