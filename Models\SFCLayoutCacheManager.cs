using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;

namespace PC_Control2.Demo.Models
{
    /// <summary>
    /// SFC布局缓存管理器实现
    /// 负责缓存布局计算结果，提高性能
    /// </summary>
    public class SFCLayoutCacheManager : ILayoutCacheManager
    {
        #region 私有字段

        /// <summary>
        /// 布局结果缓存
        /// Key: 缓存键字符串, Value: 缓存的布局结果
        /// </summary>
        private readonly ConcurrentDictionary<string, CachedLayoutResult> _layoutCache;

        /// <summary>
        /// 分支依赖关系缓存
        /// Key: 分支ID, Value: 依赖的分支ID列表
        /// </summary>
        private readonly ConcurrentDictionary<string, HashSet<string>> _dependencyCache;

        /// <summary>
        /// 缓存统计信息
        /// </summary>
        private readonly CacheStatistics _statistics;

        /// <summary>
        /// 缓存配置
        /// </summary>
        private readonly CacheConfiguration _configuration;

        /// <summary>
        /// 清理任务取消令牌源
        /// </summary>
        private readonly System.Threading.CancellationTokenSource _cleanupCancellationTokenSource;

        #endregion

        #region 构造函数

        /// <summary>
        /// 初始化布局缓存管理器
        /// </summary>
        /// <param name="configuration">缓存配置</param>
        public SFCLayoutCacheManager(CacheConfiguration? configuration = null)
        {
            _configuration = configuration ?? new CacheConfiguration();
            _layoutCache = new ConcurrentDictionary<string, CachedLayoutResult>();
            _dependencyCache = new ConcurrentDictionary<string, HashSet<string>>();
            _statistics = new CacheStatistics();
            _cleanupCancellationTokenSource = new System.Threading.CancellationTokenSource();

            // 启动定期清理任务
            if (_configuration.EnablePeriodicCleanup)
            {
                _ = Task.Run(PeriodicCleanupAsync, _cleanupCancellationTokenSource.Token);
            }
        }

        #endregion

        #region ILayoutCacheManager 实现

        /// <summary>
        /// 获取缓存的布局结果
        /// </summary>
        /// <param name="branchId">分支ID</param>
        /// <param name="key">缓存键</param>
        /// <returns>缓存的布局结果</returns>
        public CachedLayoutResult? GetCachedLayout(string branchId, LayoutCacheKey key)
        {
            if (string.IsNullOrEmpty(branchId) || key == null)
                return null;

            var cacheKey = GenerateCacheKey(branchId, key);
            if (!_layoutCache.TryGetValue(cacheKey, out var cachedResult))
            {
                _statistics.MissCount++;
                return null;
            }

            // 检查缓存是否过期
            if (cachedResult.IsExpired)
            {
                _statistics.ExpiredCount++;
                _layoutCache.TryRemove(cacheKey, out _);
                return null;
            }

            _statistics.HitCount++;
            cachedResult.LastAccessed = DateTime.Now;

            return cachedResult;
        }

        /// <summary>
        /// 缓存布局结果
        /// </summary>
        /// <param name="branchId">分支ID</param>
        /// <param name="key">缓存键</param>
        /// <param name="result">布局结果</param>
        public void CacheLayout(string branchId, LayoutCacheKey key, BranchLayoutResult result)
        {
            if (string.IsNullOrEmpty(branchId) || key == null || result == null)
                return;

            var cacheKey = GenerateCacheKey(branchId, key);
            var cachedResult = new CachedLayoutResult
            {
                BranchId = branchId,
                Key = key,
                Result = result,
                CachedAt = DateTime.Now,
                LastAccessed = DateTime.Now
            };

            _layoutCache.AddOrUpdate(cacheKey, cachedResult, (k, existing) => cachedResult);

            // 检查缓存大小限制
            if (_layoutCache.Count > _configuration.MaxCacheSize)
            {
                EvictLeastRecentlyUsed();
            }
        }

        /// <summary>
        /// 使缓存失效
        /// </summary>
        /// <param name="branchId">分支ID</param>
        /// <param name="cascadeToRelated">是否级联到相关分支</param>
        public void InvalidateCache(string branchId, bool cascadeToRelated = true)
        {
            if (string.IsNullOrEmpty(branchId))
                return;

            var keysToRemove = new List<string>();
            
            // 找到所有与该分支相关的缓存键
            foreach (var kvp in _layoutCache)
            {
                if (kvp.Value.BranchId == branchId)
                {
                    keysToRemove.Add(kvp.Key);
                }
            }

            // 移除缓存项
            foreach (var key in keysToRemove)
            {
                _layoutCache.TryRemove(key, out _);
            }

            // 级联失效相关分支
            if (cascadeToRelated)
            {
                InvalidateDependentBranches(branchId);
            }
        }

        /// <summary>
        /// 清理过期缓存
        /// </summary>
        public void CleanupExpiredCache()
        {
            var expiredKeys = new List<string>();

            foreach (var kvp in _layoutCache)
            {
                if (kvp.Value.IsExpired)
                {
                    expiredKeys.Add(kvp.Key);
                }
            }

            foreach (var key in expiredKeys)
            {
                _layoutCache.TryRemove(key, out _);
                _statistics.ExpiredCount++;
            }
        }

        /// <summary>
        /// 获取缓存统计信息
        /// </summary>
        /// <returns>缓存统计</returns>
        public CacheStatistics GetCacheStatistics()
        {
            _statistics.CacheCount = _layoutCache.Count;
            return _statistics;
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 生成缓存键
        /// </summary>
        /// <param name="branchId">分支ID</param>
        /// <param name="key">布局缓存键</param>
        /// <returns>缓存键字符串</returns>
        private string GenerateCacheKey(string branchId, LayoutCacheKey key)
        {
            return $"{branchId}_{key.GetHashCode()}";
        }

        /// <summary>
        /// 级联失效依赖指定分支的其他分支
        /// </summary>
        /// <param name="changedBranchId">发生变化的分支ID</param>
        private void InvalidateDependentBranches(string changedBranchId)
        {
            var dependentBranches = new List<string>();

            foreach (var kvp in _dependencyCache)
            {
                if (kvp.Value.Contains(changedBranchId))
                {
                    dependentBranches.Add(kvp.Key);
                }
            }

            foreach (var dependentBranch in dependentBranches)
            {
                InvalidateCache(dependentBranch, false); // 避免无限递归
            }
        }

        /// <summary>
        /// 淘汰最近最少使用的缓存项
        /// </summary>
        private void EvictLeastRecentlyUsed()
        {
            var itemsToEvict = _layoutCache.Count - _configuration.MaxCacheSize + _configuration.EvictionBatchSize;
            if (itemsToEvict <= 0) return;

            var sortedItems = _layoutCache.Values
                .OrderBy(x => x.LastAccessed)
                .Take(itemsToEvict)
                .Select(x => x.BranchId)
                .ToList();

            foreach (var branchId in sortedItems)
            {
                InvalidateCache(branchId, false);
            }
        }

        /// <summary>
        /// 定期清理过期缓存
        /// </summary>
        private async Task PeriodicCleanupAsync()
        {
            while (!_cleanupCancellationTokenSource.Token.IsCancellationRequested)
            {
                try
                {
                    await Task.Delay(_configuration.CleanupInterval, _cleanupCancellationTokenSource.Token);
                    CleanupExpiredCache();
                }
                catch (OperationCanceledException)
                {
                    break;
                }
                catch (Exception ex)
                {
                    // 记录异常但继续运行
                    System.Diagnostics.Debug.WriteLine($"缓存清理异常: {ex.Message}");
                }
            }
        }

        #endregion

        #region IDisposable 实现

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            _cleanupCancellationTokenSource?.Cancel();
            _cleanupCancellationTokenSource?.Dispose();
            
            _layoutCache.Clear();
            _dependencyCache.Clear();
        }

        #endregion
    }

    #region 缓存配置类

    /// <summary>
    /// 缓存配置
    /// </summary>
    public class CacheConfiguration
    {
        /// <summary>
        /// 最大缓存大小
        /// </summary>
        public int MaxCacheSize { get; set; } = 1000;

        /// <summary>
        /// 缓存过期时间
        /// </summary>
        public TimeSpan CacheExpiration { get; set; } = TimeSpan.FromMinutes(30);

        /// <summary>
        /// 空闲超时时间
        /// </summary>
        public TimeSpan IdleTimeout { get; set; } = TimeSpan.FromMinutes(10);

        /// <summary>
        /// 清理间隔
        /// </summary>
        public TimeSpan CleanupInterval { get; set; } = TimeSpan.FromMinutes(5);

        /// <summary>
        /// 淘汰批次大小
        /// </summary>
        public int EvictionBatchSize { get; set; } = 50;

        /// <summary>
        /// 是否启用定期清理
        /// </summary>
        public bool EnablePeriodicCleanup { get; set; } = true;
    }

    #endregion
}