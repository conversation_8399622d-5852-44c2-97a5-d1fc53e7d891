using System;
using System.Collections.Generic;
using System.Linq;

namespace PC_Control2.Demo.Models
{
    /// <summary>
    /// SFC分支层次管理器
    /// 负责管理分支的树形结构和父子关系
    /// </summary>
    public class SFCBranchHierarchyManager : ISFCBranchHierarchyManager
    {
        #region 私有字段

        /// <summary>
        /// 分支系统字典，键为根分支ID
        /// </summary>
        private readonly Dictionary<string, BranchSystem> _branchSystems = new();

        /// <summary>
        /// 分支到系统的映射，键为分支ID，值为根分支ID
        /// </summary>
        private readonly Dictionary<string, string> _branchToSystemMap = new();

        /// <summary>
        /// 需要级联更新的分支ID集合
        /// </summary>
        private readonly HashSet<string> _cascadeUpdateNeeded = new();

        #endregion

        #region 公共方法

        /// <summary>
        /// 添加嵌套分支
        /// </summary>
        /// <param name="parentBranchId">父分支ID</param>
        /// <param name="newBranch">新分支</param>
        /// <param name="insertIndex">插入位置索引</param>
        public void AddNestedBranch(string parentBranchId, SFCBranchModel newBranch, int insertIndex = -1)
        {
            if (string.IsNullOrEmpty(parentBranchId) || newBranch == null)
                return;

            // 获取或创建分支系统
            var system = GetOrCreateBranchSystem(parentBranchId);
            
            // 获取父分支节点
            if (!system.BranchNodes.TryGetValue(parentBranchId, out var parentNode))
            {
                // 如果父分支不存在，创建它
                parentNode = new BranchHierarchyNode
                {
                    BranchId = parentBranchId,
                    NestingLevel = 0
                };
                system.BranchNodes[parentBranchId] = parentNode;
                
                // 如果这是第一个分支，设为根分支
                if (string.IsNullOrEmpty(system.RootBranchId))
                {
                    system.RootBranchId = parentBranchId;
                }
            }

            // 创建新分支节点
            var newNode = new BranchHierarchyNode
            {
                BranchId = newBranch.Id,
                ParentBranchId = parentBranchId,
                NestingLevel = parentNode.NestingLevel + 1
            };

            // 添加到父分支的子分支列表
            if (insertIndex < 0 || insertIndex >= parentNode.ChildBranchIds.Count)
            {
                parentNode.ChildBranchIds.Add(newBranch.Id);
                newNode.IndexInParent = parentNode.ChildBranchIds.Count - 1;
            }
            else
            {
                parentNode.ChildBranchIds.Insert(insertIndex, newBranch.Id);
                newNode.IndexInParent = insertIndex;
                
                // 更新后续兄弟分支的索引
                UpdateSiblingIndexes(system, parentBranchId, insertIndex + 1);
            }

            // 添加到系统中
            system.BranchNodes[newBranch.Id] = newNode;
            _branchToSystemMap[newBranch.Id] = system.RootBranchId;

            // 更新分支模型的层次信息
            UpdateBranchModelHierarchy(newBranch, newNode);

            // 更新系统的最大嵌套深度
            system.MaxNestingDepth = Math.Max(system.MaxNestingDepth, newNode.NestingLevel);

            // 标记需要级联更新
            MarkCascadeUpdateNeeded(parentBranchId);
        }

        /// <summary>
        /// 移除嵌套分支
        /// </summary>
        /// <param name="branchId">分支ID</param>
        public void RemoveNestedBranch(string branchId)
        {
            if (string.IsNullOrEmpty(branchId))
                return;

            // 查找分支所在的系统
            if (!_branchToSystemMap.TryGetValue(branchId, out var rootBranchId))
                return;

            if (!_branchSystems.TryGetValue(rootBranchId, out var system))
                return;

            if (!system.BranchNodes.TryGetValue(branchId, out var branchNode))
                return;

            // 递归移除所有子分支
            var childrenToRemove = new List<string>(branchNode.ChildBranchIds);
            foreach (var childId in childrenToRemove)
            {
                RemoveNestedBranch(childId);
            }

            // 从父分支的子分支列表中移除
            if (!string.IsNullOrEmpty(branchNode.ParentBranchId) &&
                system.BranchNodes.TryGetValue(branchNode.ParentBranchId, out var parentNode))
            {
                var index = parentNode.ChildBranchIds.IndexOf(branchId);
                if (index >= 0)
                {
                    parentNode.ChildBranchIds.RemoveAt(index);
                    
                    // 更新后续兄弟分支的索引
                    UpdateSiblingIndexes(system, branchNode.ParentBranchId, index);
                    
                    // 标记父分支需要级联更新
                    MarkCascadeUpdateNeeded(branchNode.ParentBranchId);
                }
            }

            // 从系统中移除
            system.BranchNodes.Remove(branchId);
            _branchToSystemMap.Remove(branchId);

            // 如果移除的是根分支，清理整个系统
            if (branchId == system.RootBranchId)
            {
                _branchSystems.Remove(rootBranchId);
            }
            else
            {
                // 重新计算系统的最大嵌套深度
                RecalculateMaxNestingDepth(system);
            }

            // 移除级联更新标记
            _cascadeUpdateNeeded.Remove(branchId);
        }

        /// <summary>
        /// 获取分支的祖先ID列表
        /// </summary>
        /// <param name="branchId">分支ID</param>
        /// <returns>祖先ID列表</returns>
        public List<string> GetAncestorBranchIds(string branchId)
        {
            var ancestors = new List<string>();
            
            if (string.IsNullOrEmpty(branchId))
                return ancestors;

            if (!_branchToSystemMap.TryGetValue(branchId, out var rootBranchId))
                return ancestors;

            if (!_branchSystems.TryGetValue(rootBranchId, out var system))
                return ancestors;

            var currentId = branchId;
            while (!string.IsNullOrEmpty(currentId) && 
                   system.BranchNodes.TryGetValue(currentId, out var node) &&
                   !string.IsNullOrEmpty(node.ParentBranchId))
            {
                ancestors.Add(node.ParentBranchId);
                currentId = node.ParentBranchId;
            }

            return ancestors;
        }

        /// <summary>
        /// 获取分支的后代ID列表
        /// </summary>
        /// <param name="branchId">分支ID</param>
        /// <returns>后代ID列表</returns>
        public List<string> GetDescendantBranchIds(string branchId)
        {
            var descendants = new List<string>();
            
            if (string.IsNullOrEmpty(branchId))
                return descendants;

            if (!_branchToSystemMap.TryGetValue(branchId, out var rootBranchId))
                return descendants;

            if (!_branchSystems.TryGetValue(rootBranchId, out var system))
                return descendants;

            if (!system.BranchNodes.TryGetValue(branchId, out var node))
                return descendants;

            // 递归收集所有后代
            CollectDescendants(system, branchId, descendants);

            return descendants;
        }

        /// <summary>
        /// 获取分支系统
        /// </summary>
        /// <param name="branchId">分支ID</param>
        /// <returns>分支系统</returns>
        public BranchSystem? GetBranchSystem(string branchId)
        {
            if (string.IsNullOrEmpty(branchId))
                return null;

            if (!_branchToSystemMap.TryGetValue(branchId, out var rootBranchId))
                return null;

            return _branchSystems.TryGetValue(rootBranchId, out var system) ? system : null;
        }

        /// <summary>
        /// 标记需要级联更新
        /// </summary>
        /// <param name="branchId">分支ID</param>
        public void MarkCascadeUpdateNeeded(string branchId)
        {
            if (!string.IsNullOrEmpty(branchId))
            {
                _cascadeUpdateNeeded.Add(branchId);
                
                // 标记系统需要重新计算布局
                if (_branchToSystemMap.TryGetValue(branchId, out var rootBranchId) &&
                    _branchSystems.TryGetValue(rootBranchId, out var system))
                {
                    system.NeedsLayoutRecalculation = true;
                }
            }
        }

        /// <summary>
        /// 获取需要级联更新的分支ID列表
        /// </summary>
        /// <returns>需要级联更新的分支ID列表</returns>
        public List<string> GetCascadeUpdateNeededBranches()
        {
            return new List<string>(_cascadeUpdateNeeded);
        }

        /// <summary>
        /// 清除级联更新标记
        /// </summary>
        /// <param name="branchId">分支ID</param>
        public void ClearCascadeUpdateNeeded(string branchId)
        {
            _cascadeUpdateNeeded.Remove(branchId);
        }

        /// <summary>
        /// 清除所有级联更新标记
        /// </summary>
        public void ClearAllCascadeUpdateNeeded()
        {
            _cascadeUpdateNeeded.Clear();
        }

        /// <summary>
        /// 获取分支的层次节点
        /// </summary>
        /// <param name="branchId">分支ID</param>
        /// <returns>层次节点</returns>
        public BranchHierarchyNode? GetBranchHierarchyNode(string branchId)
        {
            if (string.IsNullOrEmpty(branchId))
                return null;

            if (!_branchToSystemMap.TryGetValue(branchId, out var rootBranchId))
                return null;

            if (!_branchSystems.TryGetValue(rootBranchId, out var system))
                return null;

            return system.BranchNodes.TryGetValue(branchId, out var node) ? node : null;
        }

        /// <summary>
        /// 获取所有分支系统
        /// </summary>
        /// <returns>分支系统字典</returns>
        public Dictionary<string, BranchSystem> GetAllBranchSystems()
        {
            return new Dictionary<string, BranchSystem>(_branchSystems);
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 获取或创建分支系统
        /// </summary>
        /// <param name="branchId">分支ID</param>
        /// <returns>分支系统</returns>
        private BranchSystem GetOrCreateBranchSystem(string branchId)
        {
            // 如果分支已经在某个系统中，返回该系统
            if (_branchToSystemMap.TryGetValue(branchId, out var existingRootId) &&
                _branchSystems.TryGetValue(existingRootId, out var existingSystem))
            {
                return existingSystem;
            }

            // 创建新系统
            var system = new BranchSystem
            {
                RootBranchId = branchId
            };

            _branchSystems[branchId] = system;
            _branchToSystemMap[branchId] = branchId;

            return system;
        }

        /// <summary>
        /// 更新兄弟分支的索引
        /// </summary>
        /// <param name="system">分支系统</param>
        /// <param name="parentBranchId">父分支ID</param>
        /// <param name="startIndex">开始索引</param>
        private void UpdateSiblingIndexes(BranchSystem system, string parentBranchId, int startIndex)
        {
            if (!system.BranchNodes.TryGetValue(parentBranchId, out var parentNode))
                return;

            for (int i = startIndex; i < parentNode.ChildBranchIds.Count; i++)
            {
                var childId = parentNode.ChildBranchIds[i];
                if (system.BranchNodes.TryGetValue(childId, out var childNode))
                {
                    childNode.IndexInParent = i;
                }
            }
        }

        /// <summary>
        /// 更新分支模型的层次信息
        /// </summary>
        /// <param name="branch">分支模型</param>
        /// <param name="node">层次节点</param>
        private void UpdateBranchModelHierarchy(SFCBranchModel branch, BranchHierarchyNode node)
        {
            branch.ParentBranchId = node.ParentBranchId;
            branch.NestingLevel = node.NestingLevel;
            branch.IndexInParent = node.IndexInParent;
            branch.MarkLayoutDirty();
        }

        /// <summary>
        /// 重新计算系统的最大嵌套深度
        /// </summary>
        /// <param name="system">分支系统</param>
        private void RecalculateMaxNestingDepth(BranchSystem system)
        {
            system.MaxNestingDepth = system.BranchNodes.Values
                .Select(node => node.NestingLevel)
                .DefaultIfEmpty(0)
                .Max();
        }

        /// <summary>
        /// 递归收集后代分支
        /// </summary>
        /// <param name="system">分支系统</param>
        /// <param name="branchId">分支ID</param>
        /// <param name="descendants">后代列表</param>
        private void CollectDescendants(BranchSystem system, string branchId, List<string> descendants)
        {
            if (!system.BranchNodes.TryGetValue(branchId, out var node))
                return;

            foreach (var childId in node.ChildBranchIds)
            {
                descendants.Add(childId);
                CollectDescendants(system, childId, descendants);
            }
        }

        #endregion

        #region 调试和诊断方法

        /// <summary>
        /// 获取分支层次结构的文本表示
        /// </summary>
        /// <param name="branchId">分支ID</param>
        /// <returns>层次结构文本</returns>
        public string GetHierarchyText(string branchId)
        {
            var system = GetBranchSystem(branchId);
            if (system == null)
                return "分支不存在于任何系统中";

            var result = new List<string>();
            BuildHierarchyText(system, system.RootBranchId, 0, result);
            return string.Join("\n", result);
        }

        /// <summary>
        /// 构建层次结构文本
        /// </summary>
        /// <param name="system">分支系统</param>
        /// <param name="branchId">分支ID</param>
        /// <param name="level">层级</param>
        /// <param name="result">结果列表</param>
        /// <summary>
        /// 构建层次结构文本
        /// </summary>
        /// <param name="system">分支系统</param>
        /// <param name="branchId">分支ID</param>
        /// <param name="level">层级</param>
        /// <param name="result">结果列表</param>
        private void BuildHierarchyText(BranchSystem system, string branchId, int level, List<string> result)
        {
            if (!system.BranchNodes.TryGetValue(branchId, out var node))
                return;

            var indent = new string(' ', level * 2);
            result.Add($"{indent}├─ {branchId} (Level: {node.NestingLevel}, Index: {node.IndexInParent})");

            foreach (var childId in node.ChildBranchIds)
            {
                BuildHierarchyText(system, childId, level + 1, result);
            }
        }

        /// <summary>
        /// 验证层次结构的完整性
        /// </summary>
        /// <returns>验证结果</returns>
        public List<string> ValidateHierarchyIntegrity()
        {
            var issues = new List<string>();

            foreach (var kvp in _branchSystems)
            {
                var rootId = kvp.Key;
                var system = kvp.Value;

                // 验证根分支
                if (system.RootBranchId != rootId)
                {
                    issues.Add($"系统 {rootId} 的根分支ID不匹配: {system.RootBranchId}");
                }

                // 验证每个节点
                foreach (var nodeKvp in system.BranchNodes)
                {
                    var branchId = nodeKvp.Key;
                    var node = nodeKvp.Value;

                    // 验证分支到系统的映射
                    if (!_branchToSystemMap.TryGetValue(branchId, out var mappedRootId) || mappedRootId != rootId)
                    {
                        issues.Add($"分支 {branchId} 的系统映射不正确");
                    }

                    // 验证父子关系
                    if (!string.IsNullOrEmpty(node.ParentBranchId))
                    {
                        if (!system.BranchNodes.TryGetValue(node.ParentBranchId, out var parentNode))
                        {
                            issues.Add($"分支 {branchId} 的父分支 {node.ParentBranchId} 不存在");
                        }
                        else if (!parentNode.ChildBranchIds.Contains(branchId))
                        {
                            issues.Add($"父分支 {node.ParentBranchId} 的子分支列表中不包含 {branchId}");
                        }
                    }

                    // 验证子分支关系
                    foreach (var childId in node.ChildBranchIds)
                    {
                        if (!system.BranchNodes.TryGetValue(childId, out var childNode))
                        {
                            issues.Add($"分支 {branchId} 的子分支 {childId} 不存在");
                        }
                        else if (childNode.ParentBranchId != branchId)
                        {
                            issues.Add($"子分支 {childId} 的父分支ID不正确");
                        }
                    }
                }
            }

            return issues;
        }

        #endregion
    }
}