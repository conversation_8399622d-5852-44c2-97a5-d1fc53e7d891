using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using PC_Control2.Demo.Models;
using PC_Control2.Demo.ViewModels;

namespace PC_Control2.Demo.Services
{
    /// <summary>
    /// SFC分支动态布局UI集成服务
    /// 负责协调UI更新和布局计算，将动态布局算法集成到SFC编辑器中
    /// </summary>
    public class SFCDynamicLayoutUIService : ISFCDynamicLayoutUIService
    {
        #region 私有字段

        private readonly ISFCBranchLayoutEngine _layoutEngine;
        private readonly ISFCConnectionPointSynchronizer _connectionSynchronizer;
        private readonly ISFCBranchLayoutPerformanceMonitor _performanceMonitor;

        #endregion

        #region 构造函数

        /// <summary>
        /// 初始化SFC动态布局UI服务
        /// </summary>
        public SFCDynamicLayoutUIService(
            ISFCBranchLayoutEngine layoutEngine,
            ISFCConnectionPointSynchronizer connectionSynchronizer,
            ISFCBranchLayoutPerformanceMonitor performanceMonitor)
        {
            _layoutEngine = layoutEngine ?? throw new ArgumentNullException(nameof(layoutEngine));
            _connectionSynchronizer = connectionSynchronizer ?? throw new ArgumentNullException(nameof(connectionSynchronizer));
            _performanceMonitor = performanceMonitor ?? throw new ArgumentNullException(nameof(performanceMonitor));
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 处理元素插入后的动态布局更新
        /// </summary>
        public async Task<LayoutUpdateResult> HandleElementInsertionAsync(
            object insertedElement, 
            SFCBranchModel parentBranch, 
            SFCModel sfcContext, 
            EnhancedSFCViewModel viewModel)
        {
            var monitoringId = _performanceMonitor.StartLayoutCalculationMonitoring(
                parentBranch?.Id ?? "root", 
                "ElementInsertion");

            try
            {
                var result = new LayoutUpdateResult
                {
                    IsSuccessful = true,
                    UpdatedBranches = new List<BranchUpdateInfo>(),
                    UpdatedConnections = new List<ConnectionUpdateInfo>(),
                    PerformanceMetrics = new LayoutPerformanceInfo()
                };

                // 1. 检测是否需要动态布局调整
                if (parentBranch == null || !ShouldTriggerDynamicLayout(insertedElement, parentBranch))
                {
                    result.Message = "无需动态布局调整";
                    return result;
                }

                // 2. 查找受影响的分支
                var affectedBranches = FindAffectedBranches(insertedElement, parentBranch, sfcContext);
                
                // 3. 执行级联布局计算
                var layoutResults = new Dictionary<string, Models.BranchLayoutResult>();
                foreach (var branch in affectedBranches)
                {
                    var context = new LayoutChangeContext 
                    {
                        SFCContext = sfcContext,
                        ChangeType = LayoutChangeType.ChildBranchAdded,
                        ChangedElement = insertedElement
                    };
                    
                    var layoutResult = await _layoutEngine.CalculateLayoutAsync(branch, context);
                    
                    // 转换为Models命名空间的BranchLayoutResult
                    var modelsLayoutResult = new Models.BranchLayoutResult
                    {
                        BranchId = layoutResult.BranchId,
                        IsValid = layoutResult.IsValid,
                        CalculatedSize = new Size(layoutResult.CalculatedSize.Width, layoutResult.CalculatedSize.Height),
                        CalculationTime = TimeSpan.Zero // 从PerformanceInfo获取
                    };
                    
                    layoutResults[branch.Id] = modelsLayoutResult;
                    
                    if (layoutResult.IsValid)
                    {
                        result.UpdatedBranches.Add(new BranchUpdateInfo
                        {
                            BranchId = branch.Id,
                            OldSize = branch.Size,
                            NewSize = layoutResult.CalculatedSize,
                            OldPosition = branch.Position,
                            NewPosition = branch.Position
                        });
                    }
                }

                // 4. 应用布局结果到UI
                ApplyLayoutResultsToUI(layoutResults, viewModel);

                // 5. 同步连接点位置 - 新增的关键步骤
                await SynchronizeConnectionPointsAfterLayout(affectedBranches, layoutResults, sfcContext, result);

                result.Message = $"成功更新了 {result.UpdatedBranches.Count} 个分支的布局，同步了 {result.UpdatedConnections.Count} 个连接点";
                return result;
            }
            catch (Exception ex)
            {
                return new LayoutUpdateResult
                {
                    IsSuccessful = false,
                    ErrorMessage = $"动态布局更新失败: {ex.Message}",
                    UpdatedBranches = new List<BranchUpdateInfo>(),
                    UpdatedConnections = new List<ConnectionUpdateInfo>()
                };
            }
            finally
            {
                var calculationResult = new Models.LayoutCalculationResult
                {
                    IsSuccessful = true,
                    ProcessedElementCount = 1,
                    CacheHitCount = 0,
                    CacheMissCount = 1
                };
                
                _performanceMonitor.EndLayoutCalculationMonitoring(monitoringId, calculationResult);
            }
        }

        /// <summary>
        /// 处理分支删除后的动态布局更新
        /// </summary>
        public async Task<LayoutUpdateResult> HandleElementDeletionAsync(
            object deletedElement, 
            SFCBranchModel parentBranch, 
            SFCModel sfcContext, 
            EnhancedSFCViewModel viewModel)
        {
            var result = new LayoutUpdateResult
            {
                IsSuccessful = true,
                Message = "删除后布局更新完成",
                UpdatedBranches = new List<BranchUpdateInfo>(),
                UpdatedConnections = new List<ConnectionUpdateInfo>(),
                PerformanceMetrics = new LayoutPerformanceInfo()
            };
            return result;
        }

        /// <summary>
        /// 强制刷新所有分支的动态布局
        /// </summary>
        public async Task<LayoutUpdateResult> RefreshAllBranchLayoutsAsync(
            SFCModel sfcContext, 
            EnhancedSFCViewModel viewModel)
        {
            var result = new LayoutUpdateResult
            {
                IsSuccessful = true,
                Message = "全量刷新完成",
                UpdatedBranches = new List<BranchUpdateInfo>(),
                UpdatedConnections = new List<ConnectionUpdateInfo>(),
                PerformanceMetrics = new LayoutPerformanceInfo()
            };
            return result;
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 判断是否需要触发动态布局
        /// </summary>
        private bool ShouldTriggerDynamicLayout(
            object insertedElement, 
            SFCBranchModel parentBranch)
        {
            // 如果插入的是分支元素，需要动态布局
            if (insertedElement is SFCBranchModel)
                return true;

            // 如果插入的是步骤，且父分支是选择分支或并行分支，需要动态布局
            if (insertedElement is SFCStepModel && 
                (parentBranch.BranchType == SFCBranchType.Selection || 
                 parentBranch.BranchType == SFCBranchType.Parallel))
                return true;

            return false;
        }

        /// <summary>
        /// 查找受影响的分支
        /// </summary>
        private List<SFCBranchModel> FindAffectedBranches(
            object insertedElement, 
            SFCBranchModel parentBranch, 
            SFCModel sfcContext)
        {
            var affectedBranches = new List<SFCBranchModel>();

            if (parentBranch != null)
            {
                affectedBranches.Add(parentBranch);

                // 查找所有祖先分支
                var currentBranch = parentBranch;
                while (!string.IsNullOrEmpty(currentBranch.ParentBranchId))
                {
                    var parentBranchModel = sfcContext.Branches?.FirstOrDefault(b => b.Id == currentBranch.ParentBranchId);
                    if (parentBranchModel != null)
                    {
                        affectedBranches.Add(parentBranchModel);
                        currentBranch = parentBranchModel;
                    }
                    else
                    {
                        break;
                    }
                }
            }

            return affectedBranches;
        }

        /// <summary>
        /// 将布局结果应用到UI
        /// </summary>
        private void ApplyLayoutResultsToUI(
            Dictionary<string, Models.BranchLayoutResult> layoutResults, 
            EnhancedSFCViewModel viewModel)
        {
            Application.Current.Dispatcher.Invoke(() =>
            {
                foreach (var kvp in layoutResults)
                {
                    var branchId = kvp.Key;
                    var layoutResult = kvp.Value;

                    if (!layoutResult.IsValid)
                        continue;

                    // 查找对应的分支ViewModel
                    var branchViewModel = viewModel.BranchViewModels?.FirstOrDefault(b => b.Id == branchId);
                    if (branchViewModel != null)
                    {
                        // 更新分支的尺寸
                        branchViewModel.Size = layoutResult.CalculatedSize;
                    }

                    // 更新数据模型
                    var branchModel = viewModel.CurrentSFC?.Branches?.FirstOrDefault(b => b.Id == branchId);
                    if (branchModel != null)
                    {
                        // 更新基础尺寸
                        branchModel.Size = layoutResult.CalculatedSize;
                        
                        // 更新动态布局相关属性
                        branchModel.CalculatedSize = layoutResult.CalculatedSize;
                        
                        // 计算水平线扩展长度（基于新的宽度）
                        var baseWidth = branchModel.BranchType == SFCBranchType.Selection ? 133 : 146;
                        branchModel.HorizontalLineExtension = Math.Max(baseWidth, layoutResult.CalculatedSize.Width - 20);
                        
                        System.Diagnostics.Debug.WriteLine(
                            $"[UI更新] 分支 {branchId} 尺寸更新: {layoutResult.CalculatedSize}, 水平线长度: {branchModel.HorizontalLineExtension}");
                    }
                }
            });
        }

        /// <summary>
        /// 在布局更新后同步连接点位置
        /// </summary>
        private async Task SynchronizeConnectionPointsAfterLayout(
            List<SFCBranchModel> affectedBranches,
            Dictionary<string, Models.BranchLayoutResult> layoutResults,
            SFCModel sfcContext,
            LayoutUpdateResult result)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"[连接点同步] 开始同步 {affectedBranches.Count} 个分支的连接点位置");

                foreach (var branch in affectedBranches)
                {
                    if (layoutResults.TryGetValue(branch.Id, out var layoutResult) && layoutResult.IsValid)
                    {
                        // 调用连接点同步器
                        var syncResult = await _connectionSynchronizer.SynchronizeConnectionPointsAsync(
                            branch, layoutResult, sfcContext);

                        if (syncResult.IsSuccessful)
                        {
                            // 将连接点更新信息添加到结果中
                            result.UpdatedConnections.AddRange(syncResult.UpdatedConnections);
                            
                            System.Diagnostics.Debug.WriteLine(
                                $"[连接点同步] 分支 {branch.Id} 同步成功，更新了 {syncResult.UpdatedConnections.Count} 个连接点");
                        }
                        else
                        {
                            System.Diagnostics.Debug.WriteLine(
                                $"[连接点同步] 分支 {branch.Id} 同步失败: {syncResult.ErrorMessage}");
                        }
                    }
                }

                System.Diagnostics.Debug.WriteLine($"[连接点同步] 完成，总共更新了 {result.UpdatedConnections.Count} 个连接点");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[连接点同步] 异常: {ex.Message}");
                // 不抛出异常，避免影响主要的布局更新流程
            }
        }

        #endregion
    }

    #region 接口定义

    /// <summary>
    /// SFC动态布局UI服务接口
    /// </summary>
    public interface ISFCDynamicLayoutUIService
    {
        /// <summary>
        /// 处理元素插入后的动态布局更新
        /// </summary>
        Task<LayoutUpdateResult> HandleElementInsertionAsync(
            object insertedElement, 
            SFCBranchModel parentBranch, 
            SFCModel sfcContext, 
            EnhancedSFCViewModel viewModel);

        /// <summary>
        /// 处理分支删除后的动态布局更新
        /// </summary>
        Task<LayoutUpdateResult> HandleElementDeletionAsync(
            object deletedElement, 
            SFCBranchModel parentBranch, 
            SFCModel sfcContext, 
            EnhancedSFCViewModel viewModel);

        /// <summary>
        /// 强制刷新所有分支的动态布局
        /// </summary>
        Task<LayoutUpdateResult> RefreshAllBranchLayoutsAsync(
            SFCModel sfcContext, 
            EnhancedSFCViewModel viewModel);
    }

    #endregion

    #region 数据结构

    /// <summary>
    /// 布局更新结果
    /// </summary>
    public class LayoutUpdateResult
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool IsSuccessful { get; set; }

        /// <summary>
        /// 错误消息
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// 结果消息
        /// </summary>
        public string? Message { get; set; }

        /// <summary>
        /// 更新的分支信息
        /// </summary>
        public List<BranchUpdateInfo> UpdatedBranches { get; set; } = new();

        /// <summary>
        /// 更新的连接信息
        /// </summary>
        public List<ConnectionUpdateInfo> UpdatedConnections { get; set; } = new();

        /// <summary>
        /// 性能指标
        /// </summary>
        public LayoutPerformanceInfo PerformanceMetrics { get; set; } = new();

        /// <summary>
        /// 新增：位置调整信息
        /// </summary>
        public List<ElementAdjustment> PositionAdjustments { get; set; } = new();

        /// <summary>
        /// 新增：总位移量
        /// </summary>
        public double TotalAdjustmentOffset { get; set; }
    }

    /// <summary>
    /// 分支更新信息
    /// </summary>
    public class BranchUpdateInfo
    {
        /// <summary>
        /// 分支ID
        /// </summary>
        public string BranchId { get; set; } = string.Empty;

        /// <summary>
        /// 旧尺寸
        /// </summary>
        public Size OldSize { get; set; }

        /// <summary>
        /// 新尺寸
        /// </summary>
        public Size NewSize { get; set; }

        /// <summary>
        /// 旧位置
        /// </summary>
        public Point OldPosition { get; set; }

        /// <summary>
        /// 新位置
        /// </summary>
        public Point NewPosition { get; set; }
    }

    #endregion
}