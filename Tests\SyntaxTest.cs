using System;
using PC_Control2.Demo.Services;
using PC_Control2.Demo.Models;

namespace PC_Control2.Demo.Tests
{
    /// <summary>
    /// 语法测试 - 验证新代码的语法正确性
    /// </summary>
    public class SyntaxTest
    {
        public static void TestSyntax()
        {
            try
            {
                // 测试SFCElementPositionAdjuster
                var adjuster = new SFCElementPositionAdjuster();
                Console.WriteLine("✅ SFCElementPositionAdjuster 创建成功");

                // 测试ElementAdjustment
                var adjustment = new ElementAdjustment();
                Console.WriteLine("✅ ElementAdjustment 创建成功");

                // 测试PositionAdjustmentResult
                var result = new PositionAdjustmentResult();
                Console.WriteLine("✅ PositionAdjustmentResult 创建成功");

                Console.WriteLine("🎯 所有新类的语法验证通过！");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 语法错误: {ex.Message}");
            }
        }
    }
}